//  SPDX-License-Identifier: LGPL-2.1-or-later
//  Copyright (c) 2015-2024 MariaDB Corporation Ab

// noinspection SpellCheckingInspection

'use strict';

let charsets = [];
let defaultCharsets = [];

class Collation {
  constructor(index, name, charset, maxLength) {
    this.index = index;
    this.name = name;
    this.charset = charset;
    this.maxLength = maxLength;
  }

  static fromCharset(charset) {
    return defaultCharsets[charset === 'utf8mb3' ? 'utf8' : charset];
  }

  static fromIndex(index) {
    if (index >= charsets.length) return undefined;
    return charsets[index];
  }

  static fromName(name) {
    for (let i = 0; i < charsets.length; i++) {
      let collation = charsets[i];
      if (collation && collation.name === name) {
        return collation;
      }
    }
    // for compatibility and not found, replace UTF8_ by UTF8MB4_
    const nameWithMb4 = name.replace('UTF8_', 'UTF8MB4_');
    for (let i = 0; i < charsets.length; i++) {
      let collation = charsets[i];
      if (collation && collation.name === nameWithMb4) {
        return collation;
      }
    }

    return undefined;
  }
}

// generated with query :
// SELECT CONCAT('charsets[', CAST(co.ID as char), '] = new Collation(', CAST(co.ID as char), ', \'',
// UPPER(co.COLLATION_NAME), '\', \'', co.CHARACTER_SET_NAME, '\', ', CAST(ca.MAXLEN as char), ');\n')
// FROM information_schema.COLLATION_CHARACTER_SET_APPLICABILITY co
//   LEFT OUTER JOIN information_schema.CHARACTER_SETS ca ON ca.character_set_name = co.character_set_name
// ORDER BY co.ID ASC;
// on mysql server same with COLLATIONS in place of COLLATION_CHARACTER_SET_APPLICABILITY
//then replace "utf8mb4" by "utf8"

charsets[1] = new Collation(1, 'BIG5_CHINESE_CI', 'big5', 2);
charsets[2] = new Collation(2, 'LATIN2_CZECH_CS', 'latin2', 1);
charsets[3] = new Collation(3, 'DEC8_SWEDISH_CI', 'dec8', 1);
charsets[4] = new Collation(4, 'CP850_GENERAL_CI', 'cp850', 1);
charsets[5] = new Collation(5, 'LATIN1_GERMAN1_CI', 'latin1', 1);
charsets[6] = new Collation(6, 'HP8_ENGLISH_CI', 'hp8', 1);
charsets[7] = new Collation(7, 'KOI8R_GENERAL_CI', 'koi8r', 1);
charsets[8] = new Collation(8, 'LATIN1_SWEDISH_CI', 'latin1', 1);
charsets[9] = new Collation(9, 'LATIN2_GENERAL_CI', 'latin2', 1);
charsets[10] = new Collation(10, 'SWE7_SWEDISH_CI', 'swe7', 1);
charsets[11] = new Collation(11, 'ASCII_GENERAL_CI', 'ascii', 1);
charsets[12] = new Collation(12, 'UJIS_JAPANESE_CI', 'ujis', 3);
charsets[13] = new Collation(13, 'SJIS_JAPANESE_CI', 'sjis', 2);
charsets[14] = new Collation(14, 'CP1251_BULGARIAN_CI', 'cp1251', 1);
charsets[15] = new Collation(15, 'LATIN1_DANISH_CI', 'latin1', 1);
charsets[16] = new Collation(16, 'HEBREW_GENERAL_CI', 'hebrew', 1);
charsets[18] = new Collation(18, 'TIS620_THAI_CI', 'tis620', 1);
charsets[19] = new Collation(19, 'EUCKR_KOREAN_CI', 'euckr', 2);
charsets[20] = new Collation(20, 'LATIN7_ESTONIAN_CS', 'latin7', 1);
charsets[21] = new Collation(21, 'LATIN2_HUNGARIAN_CI', 'latin2', 1);
charsets[22] = new Collation(22, 'KOI8U_GENERAL_CI', 'koi8u', 1);
charsets[23] = new Collation(23, 'CP1251_UKRAINIAN_CI', 'cp1251', 1);
charsets[24] = new Collation(24, 'GB2312_CHINESE_CI', 'gb2312', 2);
charsets[25] = new Collation(25, 'GREEK_GENERAL_CI', 'greek', 1);
charsets[26] = new Collation(26, 'CP1250_GENERAL_CI', 'cp1250', 1);
charsets[27] = new Collation(27, 'LATIN2_CROATIAN_CI', 'latin2', 1);
charsets[28] = new Collation(28, 'GBK_CHINESE_CI', 'gbk', 2);
charsets[29] = new Collation(29, 'CP1257_LITHUANIAN_CI', 'cp1257', 1);
charsets[30] = new Collation(30, 'LATIN5_TURKISH_CI', 'latin5', 1);
charsets[31] = new Collation(31, 'LATIN1_GERMAN2_CI', 'latin1', 1);
charsets[32] = new Collation(32, 'ARMSCII8_GENERAL_CI', 'armscii8', 1);
charsets[33] = new Collation(33, 'UTF8MB3_GENERAL_CI', 'utf8', 3);
charsets[34] = new Collation(34, 'CP1250_CZECH_CS', 'cp1250', 1);
charsets[35] = new Collation(35, 'UCS2_GENERAL_CI', 'ucs2', 2);
charsets[36] = new Collation(36, 'CP866_GENERAL_CI', 'cp866', 1);
charsets[37] = new Collation(37, 'KEYBCS2_GENERAL_CI', 'keybcs2', 1);
charsets[38] = new Collation(38, 'MACCE_GENERAL_CI', 'macce', 1);
charsets[39] = new Collation(39, 'MACROMAN_GENERAL_CI', 'macroman', 1);
charsets[40] = new Collation(40, 'CP852_GENERAL_CI', 'cp852', 1);
charsets[41] = new Collation(41, 'LATIN7_GENERAL_CI', 'latin7', 1);
charsets[42] = new Collation(42, 'LATIN7_GENERAL_CS', 'latin7', 1);
charsets[43] = new Collation(43, 'MACCE_BIN', 'macce', 1);
charsets[44] = new Collation(44, 'CP1250_CROATIAN_CI', 'cp1250', 1);
charsets[45] = new Collation(45, 'UTF8MB4_GENERAL_CI', 'utf8', 4);
charsets[46] = new Collation(46, 'UTF8MB4_BIN', 'utf8', 4);
charsets[47] = new Collation(47, 'LATIN1_BIN', 'latin1', 1);
charsets[48] = new Collation(48, 'LATIN1_GENERAL_CI', 'latin1', 1);
charsets[49] = new Collation(49, 'LATIN1_GENERAL_CS', 'latin1', 1);
charsets[50] = new Collation(50, 'CP1251_BIN', 'cp1251', 1);
charsets[51] = new Collation(51, 'CP1251_GENERAL_CI', 'cp1251', 1);
charsets[52] = new Collation(52, 'CP1251_GENERAL_CS', 'cp1251', 1);
charsets[53] = new Collation(53, 'MACROMAN_BIN', 'macroman', 1);
charsets[54] = new Collation(54, 'UTF16_GENERAL_CI', 'utf16', 4);
charsets[55] = new Collation(55, 'UTF16_BIN', 'utf16', 4);
charsets[56] = new Collation(56, 'UTF16LE_GENERAL_CI', 'utf16le', 4);
charsets[57] = new Collation(57, 'CP1256_GENERAL_CI', 'cp1256', 1);
charsets[58] = new Collation(58, 'CP1257_BIN', 'cp1257', 1);
charsets[59] = new Collation(59, 'CP1257_GENERAL_CI', 'cp1257', 1);
charsets[60] = new Collation(60, 'UTF32_GENERAL_CI', 'utf32', 4);
charsets[61] = new Collation(61, 'UTF32_BIN', 'utf32', 4);
charsets[62] = new Collation(62, 'UTF16LE_BIN', 'utf16le', 4);
charsets[63] = new Collation(63, 'BINARY', 'binary', 1);
charsets[64] = new Collation(64, 'ARMSCII8_BIN', 'armscii8', 1);
charsets[65] = new Collation(65, 'ASCII_BIN', 'ascii', 1);
charsets[66] = new Collation(66, 'CP1250_BIN', 'cp1250', 1);
charsets[67] = new Collation(67, 'CP1256_BIN', 'cp1256', 1);
charsets[68] = new Collation(68, 'CP866_BIN', 'cp866', 1);
charsets[69] = new Collation(69, 'DEC8_BIN', 'dec8', 1);
charsets[70] = new Collation(70, 'GREEK_BIN', 'greek', 1);
charsets[71] = new Collation(71, 'HEBREW_BIN', 'hebrew', 1);
charsets[72] = new Collation(72, 'HP8_BIN', 'hp8', 1);
charsets[73] = new Collation(73, 'KEYBCS2_BIN', 'keybcs2', 1);
charsets[74] = new Collation(74, 'KOI8R_BIN', 'koi8r', 1);
charsets[75] = new Collation(75, 'KOI8U_BIN', 'koi8u', 1);
charsets[76] = new Collation(76, 'UTF8_TOLOWER_CI', 'utf8', 3);
charsets[77] = new Collation(77, 'LATIN2_BIN', 'latin2', 1);
charsets[78] = new Collation(78, 'LATIN5_BIN', 'latin5', 1);
charsets[79] = new Collation(79, 'LATIN7_BIN', 'latin7', 1);
charsets[80] = new Collation(80, 'CP850_BIN', 'cp850', 1);
charsets[81] = new Collation(81, 'CP852_BIN', 'cp852', 1);
charsets[82] = new Collation(82, 'SWE7_BIN', 'swe7', 1);
charsets[83] = new Collation(83, 'UTF8MB3_BIN', 'utf8', 3);
charsets[84] = new Collation(84, 'BIG5_BIN', 'big5', 2);
charsets[85] = new Collation(85, 'EUCKR_BIN', 'euckr', 2);
charsets[86] = new Collation(86, 'GB2312_BIN', 'gb2312', 2);
charsets[87] = new Collation(87, 'GBK_BIN', 'gbk', 2);
charsets[88] = new Collation(88, 'SJIS_BIN', 'sjis', 2);
charsets[89] = new Collation(89, 'TIS620_BIN', 'tis620', 1);
charsets[90] = new Collation(90, 'UCS2_BIN', 'ucs2', 2);
charsets[91] = new Collation(91, 'UJIS_BIN', 'ujis', 3);
charsets[92] = new Collation(92, 'GEOSTD8_GENERAL_CI', 'geostd8', 1);
charsets[93] = new Collation(93, 'GEOSTD8_BIN', 'geostd8', 1);
charsets[94] = new Collation(94, 'LATIN1_SPANISH_CI', 'latin1', 1);
charsets[95] = new Collation(95, 'CP932_JAPANESE_CI', 'cp932', 2);
charsets[96] = new Collation(96, 'CP932_BIN', 'cp932', 2);
charsets[97] = new Collation(97, 'EUCJPMS_JAPANESE_CI', 'eucjpms', 3);
charsets[98] = new Collation(98, 'EUCJPMS_BIN', 'eucjpms', 3);
charsets[99] = new Collation(99, 'CP1250_POLISH_CI', 'cp1250', 1);
charsets[101] = new Collation(101, 'UTF16_UNICODE_CI', 'utf16', 4);
charsets[102] = new Collation(102, 'UTF16_ICELANDIC_CI', 'utf16', 4);
charsets[103] = new Collation(103, 'UTF16_LATVIAN_CI', 'utf16', 4);
charsets[104] = new Collation(104, 'UTF16_ROMANIAN_CI', 'utf16', 4);
charsets[105] = new Collation(105, 'UTF16_SLOVENIAN_CI', 'utf16', 4);
charsets[106] = new Collation(106, 'UTF16_POLISH_CI', 'utf16', 4);
charsets[107] = new Collation(107, 'UTF16_ESTONIAN_CI', 'utf16', 4);
charsets[108] = new Collation(108, 'UTF16_SPANISH_CI', 'utf16', 4);
charsets[109] = new Collation(109, 'UTF16_SWEDISH_CI', 'utf16', 4);
charsets[110] = new Collation(110, 'UTF16_TURKISH_CI', 'utf16', 4);
charsets[111] = new Collation(111, 'UTF16_CZECH_CI', 'utf16', 4);
charsets[112] = new Collation(112, 'UTF16_DANISH_CI', 'utf16', 4);
charsets[113] = new Collation(113, 'UTF16_LITHUANIAN_CI', 'utf16', 4);
charsets[114] = new Collation(114, 'UTF16_SLOVAK_CI', 'utf16', 4);
charsets[115] = new Collation(115, 'UTF16_SPANISH2_CI', 'utf16', 4);
charsets[116] = new Collation(116, 'UTF16_ROMAN_CI', 'utf16', 4);
charsets[117] = new Collation(117, 'UTF16_PERSIAN_CI', 'utf16', 4);
charsets[118] = new Collation(118, 'UTF16_ESPERANTO_CI', 'utf16', 4);
charsets[119] = new Collation(119, 'UTF16_HUNGARIAN_CI', 'utf16', 4);
charsets[120] = new Collation(120, 'UTF16_SINHALA_CI', 'utf16', 4);
charsets[121] = new Collation(121, 'UTF16_GERMAN2_CI', 'utf16', 4);
charsets[122] = new Collation(122, 'UTF16_CROATIAN_MYSQL561_CI', 'utf16', 4);
charsets[123] = new Collation(123, 'UTF16_UNICODE_520_CI', 'utf16', 4);
charsets[124] = new Collation(124, 'UTF16_VIETNAMESE_CI', 'utf16', 4);
charsets[128] = new Collation(128, 'UCS2_UNICODE_CI', 'ucs2', 2);
charsets[129] = new Collation(129, 'UCS2_ICELANDIC_CI', 'ucs2', 2);
charsets[130] = new Collation(130, 'UCS2_LATVIAN_CI', 'ucs2', 2);
charsets[131] = new Collation(131, 'UCS2_ROMANIAN_CI', 'ucs2', 2);
charsets[132] = new Collation(132, 'UCS2_SLOVENIAN_CI', 'ucs2', 2);
charsets[133] = new Collation(133, 'UCS2_POLISH_CI', 'ucs2', 2);
charsets[134] = new Collation(134, 'UCS2_ESTONIAN_CI', 'ucs2', 2);
charsets[135] = new Collation(135, 'UCS2_SPANISH_CI', 'ucs2', 2);
charsets[136] = new Collation(136, 'UCS2_SWEDISH_CI', 'ucs2', 2);
charsets[137] = new Collation(137, 'UCS2_TURKISH_CI', 'ucs2', 2);
charsets[138] = new Collation(138, 'UCS2_CZECH_CI', 'ucs2', 2);
charsets[139] = new Collation(139, 'UCS2_DANISH_CI', 'ucs2', 2);
charsets[140] = new Collation(140, 'UCS2_LITHUANIAN_CI', 'ucs2', 2);
charsets[141] = new Collation(141, 'UCS2_SLOVAK_CI', 'ucs2', 2);
charsets[142] = new Collation(142, 'UCS2_SPANISH2_CI', 'ucs2', 2);
charsets[143] = new Collation(143, 'UCS2_ROMAN_CI', 'ucs2', 2);
charsets[144] = new Collation(144, 'UCS2_PERSIAN_CI', 'ucs2', 2);
charsets[145] = new Collation(145, 'UCS2_ESPERANTO_CI', 'ucs2', 2);
charsets[146] = new Collation(146, 'UCS2_HUNGARIAN_CI', 'ucs2', 2);
charsets[147] = new Collation(147, 'UCS2_SINHALA_CI', 'ucs2', 2);
charsets[148] = new Collation(148, 'UCS2_GERMAN2_CI', 'ucs2', 2);
charsets[149] = new Collation(149, 'UCS2_CROATIAN_MYSQL561_CI', 'ucs2', 2);
charsets[150] = new Collation(150, 'UCS2_UNICODE_520_CI', 'ucs2', 2);
charsets[151] = new Collation(151, 'UCS2_VIETNAMESE_CI', 'ucs2', 2);
charsets[159] = new Collation(159, 'UCS2_GENERAL_MYSQL500_CI', 'ucs2', 2);
charsets[160] = new Collation(160, 'UTF32_UNICODE_CI', 'utf32', 4);
charsets[161] = new Collation(161, 'UTF32_ICELANDIC_CI', 'utf32', 4);
charsets[162] = new Collation(162, 'UTF32_LATVIAN_CI', 'utf32', 4);
charsets[163] = new Collation(163, 'UTF32_ROMANIAN_CI', 'utf32', 4);
charsets[164] = new Collation(164, 'UTF32_SLOVENIAN_CI', 'utf32', 4);
charsets[165] = new Collation(165, 'UTF32_POLISH_CI', 'utf32', 4);
charsets[166] = new Collation(166, 'UTF32_ESTONIAN_CI', 'utf32', 4);
charsets[167] = new Collation(167, 'UTF32_SPANISH_CI', 'utf32', 4);
charsets[168] = new Collation(168, 'UTF32_SWEDISH_CI', 'utf32', 4);
charsets[169] = new Collation(169, 'UTF32_TURKISH_CI', 'utf32', 4);
charsets[170] = new Collation(170, 'UTF32_CZECH_CI', 'utf32', 4);
charsets[171] = new Collation(171, 'UTF32_DANISH_CI', 'utf32', 4);
charsets[172] = new Collation(172, 'UTF32_LITHUANIAN_CI', 'utf32', 4);
charsets[173] = new Collation(173, 'UTF32_SLOVAK_CI', 'utf32', 4);
charsets[174] = new Collation(174, 'UTF32_SPANISH2_CI', 'utf32', 4);
charsets[175] = new Collation(175, 'UTF32_ROMAN_CI', 'utf32', 4);
charsets[176] = new Collation(176, 'UTF32_PERSIAN_CI', 'utf32', 4);
charsets[177] = new Collation(177, 'UTF32_ESPERANTO_CI', 'utf32', 4);
charsets[178] = new Collation(178, 'UTF32_HUNGARIAN_CI', 'utf32', 4);
charsets[179] = new Collation(179, 'UTF32_SINHALA_CI', 'utf32', 4);
charsets[180] = new Collation(180, 'UTF32_GERMAN2_CI', 'utf32', 4);
charsets[181] = new Collation(181, 'UTF32_CROATIAN_MYSQL561_CI', 'utf32', 4);
charsets[182] = new Collation(182, 'UTF32_UNICODE_520_CI', 'utf32', 4);
charsets[183] = new Collation(183, 'UTF32_VIETNAMESE_CI', 'utf32', 4);
charsets[192] = new Collation(192, 'UTF8MB3_UNICODE_CI', 'utf8', 3);
charsets[193] = new Collation(193, 'UTF8MB3_ICELANDIC_CI', 'utf8', 3);
charsets[194] = new Collation(194, 'UTF8MB3_LATVIAN_CI', 'utf8', 3);
charsets[195] = new Collation(195, 'UTF8MB3_ROMANIAN_CI', 'utf8', 3);
charsets[196] = new Collation(196, 'UTF8MB3_SLOVENIAN_CI', 'utf8', 3);
charsets[197] = new Collation(197, 'UTF8MB3_POLISH_CI', 'utf8', 3);
charsets[198] = new Collation(198, 'UTF8MB3_ESTONIAN_CI', 'utf8', 3);
charsets[199] = new Collation(199, 'UTF8MB3_SPANISH_CI', 'utf8', 3);
charsets[200] = new Collation(200, 'UTF8MB3_SWEDISH_CI', 'utf8', 3);
charsets[201] = new Collation(201, 'UTF8MB3_TURKISH_CI', 'utf8', 3);
charsets[202] = new Collation(202, 'UTF8MB3_CZECH_CI', 'utf8', 3);
charsets[203] = new Collation(203, 'UTF8MB3_DANISH_CI', 'utf8', 3);
charsets[204] = new Collation(204, 'UTF8MB3_LITHUANIAN_CI', 'utf8', 3);
charsets[205] = new Collation(205, 'UTF8MB3_SLOVAK_CI', 'utf8', 3);
charsets[206] = new Collation(206, 'UTF8MB3_SPANISH2_CI', 'utf8', 3);
charsets[207] = new Collation(207, 'UTF8MB3_ROMAN_CI', 'utf8', 3);
charsets[208] = new Collation(208, 'UTF8MB3_PERSIAN_CI', 'utf8', 3);
charsets[209] = new Collation(209, 'UTF8MB3_ESPERANTO_CI', 'utf8', 3);
charsets[210] = new Collation(210, 'UTF8MB3_HUNGARIAN_CI', 'utf8', 3);
charsets[211] = new Collation(211, 'UTF8MB3_SINHALA_CI', 'utf8', 3);
charsets[212] = new Collation(212, 'UTF8MB3_GERMAN2_CI', 'utf8', 3);
charsets[213] = new Collation(213, 'UTF8MB3_CROATIAN_MYSQL561_CI', 'utf8', 3);
charsets[214] = new Collation(214, 'UTF8MB3_UNICODE_520_CI', 'utf8', 3);
charsets[215] = new Collation(215, 'UTF8MB3_VIETNAMESE_CI', 'utf8', 3);
charsets[223] = new Collation(223, 'UTF8MB3_GENERAL_MYSQL500_CI', 'utf8', 3);
charsets[224] = new Collation(224, 'UTF8MB4_UNICODE_CI', 'utf8', 4);
charsets[225] = new Collation(225, 'UTF8MB4_ICELANDIC_CI', 'utf8', 4);
charsets[226] = new Collation(226, 'UTF8MB4_LATVIAN_CI', 'utf8', 4);
charsets[227] = new Collation(227, 'UTF8MB4_ROMANIAN_CI', 'utf8', 4);
charsets[228] = new Collation(228, 'UTF8MB4_SLOVENIAN_CI', 'utf8', 4);
charsets[229] = new Collation(229, 'UTF8MB4_POLISH_CI', 'utf8', 4);
charsets[230] = new Collation(230, 'UTF8MB4_ESTONIAN_CI', 'utf8', 4);
charsets[231] = new Collation(231, 'UTF8MB4_SPANISH_CI', 'utf8', 4);
charsets[232] = new Collation(232, 'UTF8MB4_SWEDISH_CI', 'utf8', 4);
charsets[233] = new Collation(233, 'UTF8MB4_TURKISH_CI', 'utf8', 4);
charsets[234] = new Collation(234, 'UTF8MB4_CZECH_CI', 'utf8', 4);
charsets[235] = new Collation(235, 'UTF8MB4_DANISH_CI', 'utf8', 4);
charsets[236] = new Collation(236, 'UTF8MB4_LITHUANIAN_CI', 'utf8', 4);
charsets[237] = new Collation(237, 'UTF8MB4_SLOVAK_CI', 'utf8', 4);
charsets[238] = new Collation(238, 'UTF8MB4_SPANISH2_CI', 'utf8', 4);
charsets[239] = new Collation(239, 'UTF8MB4_ROMAN_CI', 'utf8', 4);
charsets[240] = new Collation(240, 'UTF8MB4_PERSIAN_CI', 'utf8', 4);
charsets[241] = new Collation(241, 'UTF8MB4_ESPERANTO_CI', 'utf8', 4);
charsets[242] = new Collation(242, 'UTF8MB4_HUNGARIAN_CI', 'utf8', 4);
charsets[243] = new Collation(243, 'UTF8MB4_SINHALA_CI', 'utf8', 4);
charsets[244] = new Collation(244, 'UTF8MB4_GERMAN2_CI', 'utf8', 4);
charsets[245] = new Collation(245, 'UTF8MB4_CROATIAN_MYSQL561_CI', 'utf8', 4);
charsets[246] = new Collation(246, 'UTF8MB4_UNICODE_520_CI', 'utf8', 4);
charsets[247] = new Collation(247, 'UTF8MB4_VIETNAMESE_CI', 'utf8', 4);
charsets[248] = new Collation(248, 'GB18030_CHINESE_CI', 'gb18030', 4);
charsets[249] = new Collation(249, 'GB18030_BIN', 'gb18030', 4);
charsets[250] = new Collation(250, 'GB18030_UNICODE_520_CI', 'gb18030', 4);
charsets[255] = new Collation(255, 'UTF8MB4_0900_AI_CI', 'utf8', 4);
charsets[256] = new Collation(256, 'UTF8MB4_DE_PB_0900_AI_CI', 'utf8', 4);
charsets[257] = new Collation(257, 'UTF8MB4_IS_0900_AI_CI', 'utf8', 4);
charsets[258] = new Collation(258, 'UTF8MB4_LV_0900_AI_CI', 'utf8', 4);
charsets[259] = new Collation(259, 'UTF8MB4_RO_0900_AI_CI', 'utf8', 4);
charsets[260] = new Collation(260, 'UTF8MB4_SL_0900_AI_CI', 'utf8', 4);
charsets[261] = new Collation(261, 'UTF8MB4_PL_0900_AI_CI', 'utf8', 4);
charsets[262] = new Collation(262, 'UTF8MB4_ET_0900_AI_CI', 'utf8', 4);
charsets[263] = new Collation(263, 'UTF8MB4_ES_0900_AI_CI', 'utf8', 4);
charsets[264] = new Collation(264, 'UTF8MB4_SV_0900_AI_CI', 'utf8', 4);
charsets[265] = new Collation(265, 'UTF8MB4_TR_0900_AI_CI', 'utf8', 4);
charsets[266] = new Collation(266, 'UTF8MB4_CS_0900_AI_CI', 'utf8', 4);
charsets[267] = new Collation(267, 'UTF8MB4_DA_0900_AI_CI', 'utf8', 4);
charsets[268] = new Collation(268, 'UTF8MB4_LT_0900_AI_CI', 'utf8', 4);
charsets[269] = new Collation(269, 'UTF8MB4_SK_0900_AI_CI', 'utf8', 4);
charsets[270] = new Collation(270, 'UTF8MB4_ES_TRAD_0900_AI_CI', 'utf8', 4);
charsets[271] = new Collation(271, 'UTF8MB4_LA_0900_AI_CI', 'utf8', 4);
charsets[273] = new Collation(273, 'UTF8MB4_EO_0900_AI_CI', 'utf8', 4);
charsets[274] = new Collation(274, 'UTF8MB4_HU_0900_AI_CI', 'utf8', 4);
charsets[275] = new Collation(275, 'UTF8MB4_HR_0900_AI_CI', 'utf8', 4);
charsets[277] = new Collation(277, 'UTF8MB4_VI_0900_AI_CI', 'utf8', 4);
charsets[278] = new Collation(278, 'UTF8MB4_0900_AS_CS', 'utf8', 4);
charsets[279] = new Collation(279, 'UTF8MB4_DE_PB_0900_AS_CS', 'utf8', 4);
charsets[280] = new Collation(280, 'UTF8MB4_IS_0900_AS_CS', 'utf8', 4);
charsets[281] = new Collation(281, 'UTF8MB4_LV_0900_AS_CS', 'utf8', 4);
charsets[282] = new Collation(282, 'UTF8MB4_RO_0900_AS_CS', 'utf8', 4);
charsets[283] = new Collation(283, 'UTF8MB4_SL_0900_AS_CS', 'utf8', 4);
charsets[284] = new Collation(284, 'UTF8MB4_PL_0900_AS_CS', 'utf8', 4);
charsets[285] = new Collation(285, 'UTF8MB4_ET_0900_AS_CS', 'utf8', 4);
charsets[286] = new Collation(286, 'UTF8MB4_ES_0900_AS_CS', 'utf8', 4);
charsets[287] = new Collation(287, 'UTF8MB4_SV_0900_AS_CS', 'utf8', 4);
charsets[288] = new Collation(288, 'UTF8MB4_TR_0900_AS_CS', 'utf8', 4);
charsets[289] = new Collation(289, 'UTF8MB4_CS_0900_AS_CS', 'utf8', 4);
charsets[290] = new Collation(290, 'UTF8MB4_DA_0900_AS_CS', 'utf8', 4);
charsets[291] = new Collation(291, 'UTF8MB4_LT_0900_AS_CS', 'utf8', 4);
charsets[292] = new Collation(292, 'UTF8MB4_SK_0900_AS_CS', 'utf8', 4);
charsets[293] = new Collation(293, 'UTF8MB4_ES_TRAD_0900_AS_CS', 'utf8', 4);
charsets[294] = new Collation(294, 'UTF8MB4_LA_0900_AS_CS', 'utf8', 4);
charsets[296] = new Collation(296, 'UTF8MB4_EO_0900_AS_CS', 'utf8', 4);
charsets[297] = new Collation(297, 'UTF8MB4_HU_0900_AS_CS', 'utf8', 4);
charsets[298] = new Collation(298, 'UTF8MB4_HR_0900_AS_CS', 'utf8', 4);
charsets[300] = new Collation(300, 'UTF8MB4_VI_0900_AS_CS', 'utf8', 4);
charsets[303] = new Collation(303, 'UTF8MB4_JA_0900_AS_CS', 'utf8', 4);
charsets[304] = new Collation(304, 'UTF8MB4_JA_0900_AS_CS_KS', 'utf8', 4);
charsets[305] = new Collation(305, 'UTF8MB4_0900_AS_CI', 'utf8', 4);
charsets[306] = new Collation(306, 'UTF8MB4_RU_0900_AI_CI', 'utf8', 4);
charsets[307] = new Collation(307, 'UTF8MB4_RU_0900_AS_CS', 'utf8', 4);
charsets[308] = new Collation(308, 'UTF8MB4_ZH_0900_AS_CS', 'utf8', 4);
charsets[309] = new Collation(309, 'UTF8MB4_0900_BIN', 'utf8', 4);
charsets[576] = new Collation(576, 'UTF8MB3_CROATIAN_CI', 'utf8', 3);
charsets[577] = new Collation(577, 'UTF8MB3_MYANMAR_CI', 'utf8', 3);
charsets[578] = new Collation(578, 'UTF8MB3_THAI_520_W2', 'utf8', 3);
charsets[608] = new Collation(608, 'UTF8MB4_CROATIAN_CI', 'utf8', 4);
charsets[609] = new Collation(609, 'UTF8MB4_MYANMAR_CI', 'utf8', 4);
charsets[610] = new Collation(610, 'UTF8MB4_THAI_520_W2', 'utf8', 4);
charsets[640] = new Collation(640, 'UCS2_CROATIAN_CI', 'ucs2', 2);
charsets[641] = new Collation(641, 'UCS2_MYANMAR_CI', 'ucs2', 2);
charsets[642] = new Collation(642, 'UCS2_THAI_520_W2', 'ucs2', 2);
charsets[672] = new Collation(672, 'UTF16_CROATIAN_CI', 'utf16', 4);
charsets[673] = new Collation(673, 'UTF16_MYANMAR_CI', 'utf16', 4);
charsets[674] = new Collation(674, 'UTF16_THAI_520_W2', 'utf16', 4);
charsets[736] = new Collation(736, 'UTF32_CROATIAN_CI', 'utf32', 4);
charsets[737] = new Collation(737, 'UTF32_MYANMAR_CI', 'utf32', 4);
charsets[738] = new Collation(738, 'UTF32_THAI_520_W2', 'utf32', 4);
charsets[1025] = new Collation(1025, 'BIG5_CHINESE_NOPAD_CI', 'big5', 2);
charsets[1027] = new Collation(1027, 'DEC8_SWEDISH_NOPAD_CI', 'dec8', 1);
charsets[1028] = new Collation(1028, 'CP850_GENERAL_NOPAD_CI', 'cp850', 1);
charsets[1030] = new Collation(1030, 'HP8_ENGLISH_NOPAD_CI', 'hp8', 1);
charsets[1031] = new Collation(1031, 'KOI8R_GENERAL_NOPAD_CI', 'koi8r', 1);
charsets[1032] = new Collation(1032, 'LATIN1_SWEDISH_NOPAD_CI', 'latin1', 1);
charsets[1033] = new Collation(1033, 'LATIN2_GENERAL_NOPAD_CI', 'latin2', 1);
charsets[1034] = new Collation(1034, 'SWE7_SWEDISH_NOPAD_CI', 'swe7', 1);
charsets[1035] = new Collation(1035, 'ASCII_GENERAL_NOPAD_CI', 'ascii', 1);
charsets[1036] = new Collation(1036, 'UJIS_JAPANESE_NOPAD_CI', 'ujis', 3);
charsets[1037] = new Collation(1037, 'SJIS_JAPANESE_NOPAD_CI', 'sjis', 2);
charsets[1040] = new Collation(1040, 'HEBREW_GENERAL_NOPAD_CI', 'hebrew', 1);
charsets[1042] = new Collation(1042, 'TIS620_THAI_NOPAD_CI', 'tis620', 1);
charsets[1043] = new Collation(1043, 'EUCKR_KOREAN_NOPAD_CI', 'euckr', 2);
charsets[1046] = new Collation(1046, 'KOI8U_GENERAL_NOPAD_CI', 'koi8u', 1);
charsets[1048] = new Collation(1048, 'GB2312_CHINESE_NOPAD_CI', 'gb2312', 2);
charsets[1049] = new Collation(1049, 'GREEK_GENERAL_NOPAD_CI', 'greek', 1);
charsets[1050] = new Collation(1050, 'CP1250_GENERAL_NOPAD_CI', 'cp1250', 1);
charsets[1052] = new Collation(1052, 'GBK_CHINESE_NOPAD_CI', 'gbk', 2);
charsets[1054] = new Collation(1054, 'LATIN5_TURKISH_NOPAD_CI', 'latin5', 1);
charsets[1056] = new Collation(1056, 'ARMSCII8_GENERAL_NOPAD_CI', 'armscii8', 1);
charsets[1057] = new Collation(1057, 'UTF8MB3_GENERAL_NOPAD_CI', 'utf8', 3);
charsets[1059] = new Collation(1059, 'UCS2_GENERAL_NOPAD_CI', 'ucs2', 2);
charsets[1060] = new Collation(1060, 'CP866_GENERAL_NOPAD_CI', 'cp866', 1);
charsets[1061] = new Collation(1061, 'KEYBCS2_GENERAL_NOPAD_CI', 'keybcs2', 1);
charsets[1062] = new Collation(1062, 'MACCE_GENERAL_NOPAD_CI', 'macce', 1);
charsets[1063] = new Collation(1063, 'MACROMAN_GENERAL_NOPAD_CI', 'macroman', 1);
charsets[1064] = new Collation(1064, 'CP852_GENERAL_NOPAD_CI', 'cp852', 1);
charsets[1065] = new Collation(1065, 'LATIN7_GENERAL_NOPAD_CI', 'latin7', 1);
charsets[1067] = new Collation(1067, 'MACCE_NOPAD_BIN', 'macce', 1);
charsets[1069] = new Collation(1069, 'UTF8MB4_GENERAL_NOPAD_CI', 'utf8', 4);
charsets[1070] = new Collation(1070, 'UTF8MB4_NOPAD_BIN', 'utf8', 4);
charsets[1071] = new Collation(1071, 'LATIN1_NOPAD_BIN', 'latin1', 1);
charsets[1074] = new Collation(1074, 'CP1251_NOPAD_BIN', 'cp1251', 1);
charsets[1075] = new Collation(1075, 'CP1251_GENERAL_NOPAD_CI', 'cp1251', 1);
charsets[1077] = new Collation(1077, 'MACROMAN_NOPAD_BIN', 'macroman', 1);
charsets[1078] = new Collation(1078, 'UTF16_GENERAL_NOPAD_CI', 'utf16', 4);
charsets[1079] = new Collation(1079, 'UTF16_NOPAD_BIN', 'utf16', 4);
charsets[1080] = new Collation(1080, 'UTF16LE_GENERAL_NOPAD_CI', 'utf16le', 4);
charsets[1081] = new Collation(1081, 'CP1256_GENERAL_NOPAD_CI', 'cp1256', 1);
charsets[1082] = new Collation(1082, 'CP1257_NOPAD_BIN', 'cp1257', 1);
charsets[1083] = new Collation(1083, 'CP1257_GENERAL_NOPAD_CI', 'cp1257', 1);
charsets[1084] = new Collation(1084, 'UTF32_GENERAL_NOPAD_CI', 'utf32', 4);
charsets[1085] = new Collation(1085, 'UTF32_NOPAD_BIN', 'utf32', 4);
charsets[1086] = new Collation(1086, 'UTF16LE_NOPAD_BIN', 'utf16le', 4);
charsets[1088] = new Collation(1088, 'ARMSCII8_NOPAD_BIN', 'armscii8', 1);
charsets[1089] = new Collation(1089, 'ASCII_NOPAD_BIN', 'ascii', 1);
charsets[1090] = new Collation(1090, 'CP1250_NOPAD_BIN', 'cp1250', 1);
charsets[1091] = new Collation(1091, 'CP1256_NOPAD_BIN', 'cp1256', 1);
charsets[1092] = new Collation(1092, 'CP866_NOPAD_BIN', 'cp866', 1);
charsets[1093] = new Collation(1093, 'DEC8_NOPAD_BIN', 'dec8', 1);
charsets[1094] = new Collation(1094, 'GREEK_NOPAD_BIN', 'greek', 1);
charsets[1095] = new Collation(1095, 'HEBREW_NOPAD_BIN', 'hebrew', 1);
charsets[1096] = new Collation(1096, 'HP8_NOPAD_BIN', 'hp8', 1);
charsets[1097] = new Collation(1097, 'KEYBCS2_NOPAD_BIN', 'keybcs2', 1);
charsets[1098] = new Collation(1098, 'KOI8R_NOPAD_BIN', 'koi8r', 1);
charsets[1099] = new Collation(1099, 'KOI8U_NOPAD_BIN', 'koi8u', 1);
charsets[1101] = new Collation(1101, 'LATIN2_NOPAD_BIN', 'latin2', 1);
charsets[1102] = new Collation(1102, 'LATIN5_NOPAD_BIN', 'latin5', 1);
charsets[1103] = new Collation(1103, 'LATIN7_NOPAD_BIN', 'latin7', 1);
charsets[1104] = new Collation(1104, 'CP850_NOPAD_BIN', 'cp850', 1);
charsets[1105] = new Collation(1105, 'CP852_NOPAD_BIN', 'cp852', 1);
charsets[1106] = new Collation(1106, 'SWE7_NOPAD_BIN', 'swe7', 1);
charsets[1107] = new Collation(1107, 'UTF8MB3_NOPAD_BIN', 'utf8', 3);
charsets[1108] = new Collation(1108, 'BIG5_NOPAD_BIN', 'big5', 2);
charsets[1109] = new Collation(1109, 'EUCKR_NOPAD_BIN', 'euckr', 2);
charsets[1110] = new Collation(1110, 'GB2312_NOPAD_BIN', 'gb2312', 2);
charsets[1111] = new Collation(1111, 'GBK_NOPAD_BIN', 'gbk', 2);
charsets[1112] = new Collation(1112, 'SJIS_NOPAD_BIN', 'sjis', 2);
charsets[1113] = new Collation(1113, 'TIS620_NOPAD_BIN', 'tis620', 1);
charsets[1114] = new Collation(1114, 'UCS2_NOPAD_BIN', 'ucs2', 2);
charsets[1115] = new Collation(1115, 'UJIS_NOPAD_BIN', 'ujis', 3);
charsets[1116] = new Collation(1116, 'GEOSTD8_GENERAL_NOPAD_CI', 'geostd8', 1);
charsets[1117] = new Collation(1117, 'GEOSTD8_NOPAD_BIN', 'geostd8', 1);
charsets[1119] = new Collation(1119, 'CP932_JAPANESE_NOPAD_CI', 'cp932', 2);
charsets[1120] = new Collation(1120, 'CP932_NOPAD_BIN', 'cp932', 2);
charsets[1121] = new Collation(1121, 'EUCJPMS_JAPANESE_NOPAD_CI', 'eucjpms', 3);
charsets[1122] = new Collation(1122, 'EUCJPMS_NOPAD_BIN', 'eucjpms', 3);
charsets[1125] = new Collation(1125, 'UTF16_UNICODE_NOPAD_CI', 'utf16', 4);
charsets[1147] = new Collation(1147, 'UTF16_UNICODE_520_NOPAD_CI', 'utf16', 4);
charsets[1152] = new Collation(1152, 'UCS2_UNICODE_NOPAD_CI', 'ucs2', 2);
charsets[1174] = new Collation(1174, 'UCS2_UNICODE_520_NOPAD_CI', 'ucs2', 2);
charsets[1184] = new Collation(1184, 'UTF32_UNICODE_NOPAD_CI', 'utf32', 4);
charsets[1206] = new Collation(1206, 'UTF32_UNICODE_520_NOPAD_CI', 'utf32', 4);
charsets[1216] = new Collation(1216, 'UTF8MB3_UNICODE_NOPAD_CI', 'utf8', 3);
charsets[1238] = new Collation(1238, 'UTF8MB3_UNICODE_520_NOPAD_CI', 'utf8', 3);
charsets[1248] = new Collation(1248, 'UTF8MB4_UNICODE_NOPAD_CI', 'utf8', 4);
charsets[1270] = new Collation(1270, 'UTF8MB4_UNICODE_520_NOPAD_CI', 'utf8', 4);
charsets[2048] = new Collation(2048, 'UCA1400_AI_CI', 'utf8', 3);
charsets[2049] = new Collation(2049, 'UCA1400_AI_CS', 'utf8', 3);
charsets[2050] = new Collation(2050, 'UCA1400_AS_CI', 'utf8', 3);
charsets[2051] = new Collation(2051, 'UCA1400_AS_CS', 'utf8', 3);
charsets[2052] = new Collation(2052, 'UCA1400_NOPAD_AI_CI', 'utf8', 3);
charsets[2053] = new Collation(2053, 'UCA1400_NOPAD_AI_CS', 'utf8', 3);
charsets[2054] = new Collation(2054, 'UCA1400_NOPAD_AS_CI', 'utf8', 3);
charsets[2055] = new Collation(2055, 'UCA1400_NOPAD_AS_CS', 'utf8', 3);
charsets[2056] = new Collation(2056, 'UCA1400_ICELANDIC_AI_CI', 'utf8', 3);
charsets[2057] = new Collation(2057, 'UCA1400_ICELANDIC_AI_CS', 'utf8', 3);
charsets[2058] = new Collation(2058, 'UCA1400_ICELANDIC_AS_CI', 'utf8', 3);
charsets[2059] = new Collation(2059, 'UCA1400_ICELANDIC_AS_CS', 'utf8', 3);
charsets[2060] = new Collation(2060, 'UCA1400_ICELANDIC_NOPAD_AI_CI', 'utf8', 3);
charsets[2061] = new Collation(2061, 'UCA1400_ICELANDIC_NOPAD_AI_CS', 'utf8', 3);
charsets[2062] = new Collation(2062, 'UCA1400_ICELANDIC_NOPAD_AS_CI', 'utf8', 3);
charsets[2063] = new Collation(2063, 'UCA1400_ICELANDIC_NOPAD_AS_CS', 'utf8', 3);
charsets[2064] = new Collation(2064, 'UCA1400_LATVIAN_AI_CI', 'utf8', 3);
charsets[2065] = new Collation(2065, 'UCA1400_LATVIAN_AI_CS', 'utf8', 3);
charsets[2066] = new Collation(2066, 'UCA1400_LATVIAN_AS_CI', 'utf8', 3);
charsets[2067] = new Collation(2067, 'UCA1400_LATVIAN_AS_CS', 'utf8', 3);
charsets[2068] = new Collation(2068, 'UCA1400_LATVIAN_NOPAD_AI_CI', 'utf8', 3);
charsets[2069] = new Collation(2069, 'UCA1400_LATVIAN_NOPAD_AI_CS', 'utf8', 3);
charsets[2070] = new Collation(2070, 'UCA1400_LATVIAN_NOPAD_AS_CI', 'utf8', 3);
charsets[2071] = new Collation(2071, 'UCA1400_LATVIAN_NOPAD_AS_CS', 'utf8', 3);
charsets[2072] = new Collation(2072, 'UCA1400_ROMANIAN_AI_CI', 'utf8', 3);
charsets[2073] = new Collation(2073, 'UCA1400_ROMANIAN_AI_CS', 'utf8', 3);
charsets[2074] = new Collation(2074, 'UCA1400_ROMANIAN_AS_CI', 'utf8', 3);
charsets[2075] = new Collation(2075, 'UCA1400_ROMANIAN_AS_CS', 'utf8', 3);
charsets[2076] = new Collation(2076, 'UCA1400_ROMANIAN_NOPAD_AI_CI', 'utf8', 3);
charsets[2077] = new Collation(2077, 'UCA1400_ROMANIAN_NOPAD_AI_CS', 'utf8', 3);
charsets[2078] = new Collation(2078, 'UCA1400_ROMANIAN_NOPAD_AS_CI', 'utf8', 3);
charsets[2079] = new Collation(2079, 'UCA1400_ROMANIAN_NOPAD_AS_CS', 'utf8', 3);
charsets[2080] = new Collation(2080, 'UCA1400_SLOVENIAN_AI_CI', 'utf8', 3);
charsets[2081] = new Collation(2081, 'UCA1400_SLOVENIAN_AI_CS', 'utf8', 3);
charsets[2082] = new Collation(2082, 'UCA1400_SLOVENIAN_AS_CI', 'utf8', 3);
charsets[2083] = new Collation(2083, 'UCA1400_SLOVENIAN_AS_CS', 'utf8', 3);
charsets[2084] = new Collation(2084, 'UCA1400_SLOVENIAN_NOPAD_AI_CI', 'utf8', 3);
charsets[2085] = new Collation(2085, 'UCA1400_SLOVENIAN_NOPAD_AI_CS', 'utf8', 3);
charsets[2086] = new Collation(2086, 'UCA1400_SLOVENIAN_NOPAD_AS_CI', 'utf8', 3);
charsets[2087] = new Collation(2087, 'UCA1400_SLOVENIAN_NOPAD_AS_CS', 'utf8', 3);
charsets[2088] = new Collation(2088, 'UCA1400_POLISH_AI_CI', 'utf8', 3);
charsets[2089] = new Collation(2089, 'UCA1400_POLISH_AI_CS', 'utf8', 3);
charsets[2090] = new Collation(2090, 'UCA1400_POLISH_AS_CI', 'utf8', 3);
charsets[2091] = new Collation(2091, 'UCA1400_POLISH_AS_CS', 'utf8', 3);
charsets[2092] = new Collation(2092, 'UCA1400_POLISH_NOPAD_AI_CI', 'utf8', 3);
charsets[2093] = new Collation(2093, 'UCA1400_POLISH_NOPAD_AI_CS', 'utf8', 3);
charsets[2094] = new Collation(2094, 'UCA1400_POLISH_NOPAD_AS_CI', 'utf8', 3);
charsets[2095] = new Collation(2095, 'UCA1400_POLISH_NOPAD_AS_CS', 'utf8', 3);
charsets[2096] = new Collation(2096, 'UCA1400_ESTONIAN_AI_CI', 'utf8', 3);
charsets[2097] = new Collation(2097, 'UCA1400_ESTONIAN_AI_CS', 'utf8', 3);
charsets[2098] = new Collation(2098, 'UCA1400_ESTONIAN_AS_CI', 'utf8', 3);
charsets[2099] = new Collation(2099, 'UCA1400_ESTONIAN_AS_CS', 'utf8', 3);
charsets[2100] = new Collation(2100, 'UCA1400_ESTONIAN_NOPAD_AI_CI', 'utf8', 3);
charsets[2101] = new Collation(2101, 'UCA1400_ESTONIAN_NOPAD_AI_CS', 'utf8', 3);
charsets[2102] = new Collation(2102, 'UCA1400_ESTONIAN_NOPAD_AS_CI', 'utf8', 3);
charsets[2103] = new Collation(2103, 'UCA1400_ESTONIAN_NOPAD_AS_CS', 'utf8', 3);
charsets[2104] = new Collation(2104, 'UCA1400_SPANISH_AI_CI', 'utf8', 3);
charsets[2105] = new Collation(2105, 'UCA1400_SPANISH_AI_CS', 'utf8', 3);
charsets[2106] = new Collation(2106, 'UCA1400_SPANISH_AS_CI', 'utf8', 3);
charsets[2107] = new Collation(2107, 'UCA1400_SPANISH_AS_CS', 'utf8', 3);
charsets[2108] = new Collation(2108, 'UCA1400_SPANISH_NOPAD_AI_CI', 'utf8', 3);
charsets[2109] = new Collation(2109, 'UCA1400_SPANISH_NOPAD_AI_CS', 'utf8', 3);
charsets[2110] = new Collation(2110, 'UCA1400_SPANISH_NOPAD_AS_CI', 'utf8', 3);
charsets[2111] = new Collation(2111, 'UCA1400_SPANISH_NOPAD_AS_CS', 'utf8', 3);
charsets[2112] = new Collation(2112, 'UCA1400_SWEDISH_AI_CI', 'utf8', 3);
charsets[2113] = new Collation(2113, 'UCA1400_SWEDISH_AI_CS', 'utf8', 3);
charsets[2114] = new Collation(2114, 'UCA1400_SWEDISH_AS_CI', 'utf8', 3);
charsets[2115] = new Collation(2115, 'UCA1400_SWEDISH_AS_CS', 'utf8', 3);
charsets[2116] = new Collation(2116, 'UCA1400_SWEDISH_NOPAD_AI_CI', 'utf8', 3);
charsets[2117] = new Collation(2117, 'UCA1400_SWEDISH_NOPAD_AI_CS', 'utf8', 3);
charsets[2118] = new Collation(2118, 'UCA1400_SWEDISH_NOPAD_AS_CI', 'utf8', 3);
charsets[2119] = new Collation(2119, 'UCA1400_SWEDISH_NOPAD_AS_CS', 'utf8', 3);
charsets[2120] = new Collation(2120, 'UCA1400_TURKISH_AI_CI', 'utf8', 3);
charsets[2121] = new Collation(2121, 'UCA1400_TURKISH_AI_CS', 'utf8', 3);
charsets[2122] = new Collation(2122, 'UCA1400_TURKISH_AS_CI', 'utf8', 3);
charsets[2123] = new Collation(2123, 'UCA1400_TURKISH_AS_CS', 'utf8', 3);
charsets[2124] = new Collation(2124, 'UCA1400_TURKISH_NOPAD_AI_CI', 'utf8', 3);
charsets[2125] = new Collation(2125, 'UCA1400_TURKISH_NOPAD_AI_CS', 'utf8', 3);
charsets[2126] = new Collation(2126, 'UCA1400_TURKISH_NOPAD_AS_CI', 'utf8', 3);
charsets[2127] = new Collation(2127, 'UCA1400_TURKISH_NOPAD_AS_CS', 'utf8', 3);
charsets[2128] = new Collation(2128, 'UCA1400_CZECH_AI_CI', 'utf8', 3);
charsets[2129] = new Collation(2129, 'UCA1400_CZECH_AI_CS', 'utf8', 3);
charsets[2130] = new Collation(2130, 'UCA1400_CZECH_AS_CI', 'utf8', 3);
charsets[2131] = new Collation(2131, 'UCA1400_CZECH_AS_CS', 'utf8', 3);
charsets[2132] = new Collation(2132, 'UCA1400_CZECH_NOPAD_AI_CI', 'utf8', 3);
charsets[2133] = new Collation(2133, 'UCA1400_CZECH_NOPAD_AI_CS', 'utf8', 3);
charsets[2134] = new Collation(2134, 'UCA1400_CZECH_NOPAD_AS_CI', 'utf8', 3);
charsets[2135] = new Collation(2135, 'UCA1400_CZECH_NOPAD_AS_CS', 'utf8', 3);
charsets[2136] = new Collation(2136, 'UCA1400_DANISH_AI_CI', 'utf8', 3);
charsets[2137] = new Collation(2137, 'UCA1400_DANISH_AI_CS', 'utf8', 3);
charsets[2138] = new Collation(2138, 'UCA1400_DANISH_AS_CI', 'utf8', 3);
charsets[2139] = new Collation(2139, 'UCA1400_DANISH_AS_CS', 'utf8', 3);
charsets[2140] = new Collation(2140, 'UCA1400_DANISH_NOPAD_AI_CI', 'utf8', 3);
charsets[2141] = new Collation(2141, 'UCA1400_DANISH_NOPAD_AI_CS', 'utf8', 3);
charsets[2142] = new Collation(2142, 'UCA1400_DANISH_NOPAD_AS_CI', 'utf8', 3);
charsets[2143] = new Collation(2143, 'UCA1400_DANISH_NOPAD_AS_CS', 'utf8', 3);
charsets[2144] = new Collation(2144, 'UCA1400_LITHUANIAN_AI_CI', 'utf8', 3);
charsets[2145] = new Collation(2145, 'UCA1400_LITHUANIAN_AI_CS', 'utf8', 3);
charsets[2146] = new Collation(2146, 'UCA1400_LITHUANIAN_AS_CI', 'utf8', 3);
charsets[2147] = new Collation(2147, 'UCA1400_LITHUANIAN_AS_CS', 'utf8', 3);
charsets[2148] = new Collation(2148, 'UCA1400_LITHUANIAN_NOPAD_AI_CI', 'utf8', 3);
charsets[2149] = new Collation(2149, 'UCA1400_LITHUANIAN_NOPAD_AI_CS', 'utf8', 3);
charsets[2150] = new Collation(2150, 'UCA1400_LITHUANIAN_NOPAD_AS_CI', 'utf8', 3);
charsets[2151] = new Collation(2151, 'UCA1400_LITHUANIAN_NOPAD_AS_CS', 'utf8', 3);
charsets[2152] = new Collation(2152, 'UCA1400_SLOVAK_AI_CI', 'utf8', 3);
charsets[2153] = new Collation(2153, 'UCA1400_SLOVAK_AI_CS', 'utf8', 3);
charsets[2154] = new Collation(2154, 'UCA1400_SLOVAK_AS_CI', 'utf8', 3);
charsets[2155] = new Collation(2155, 'UCA1400_SLOVAK_AS_CS', 'utf8', 3);
charsets[2156] = new Collation(2156, 'UCA1400_SLOVAK_NOPAD_AI_CI', 'utf8', 3);
charsets[2157] = new Collation(2157, 'UCA1400_SLOVAK_NOPAD_AI_CS', 'utf8', 3);
charsets[2158] = new Collation(2158, 'UCA1400_SLOVAK_NOPAD_AS_CI', 'utf8', 3);
charsets[2159] = new Collation(2159, 'UCA1400_SLOVAK_NOPAD_AS_CS', 'utf8', 3);
charsets[2160] = new Collation(2160, 'UCA1400_SPANISH2_AI_CI', 'utf8', 3);
charsets[2161] = new Collation(2161, 'UCA1400_SPANISH2_AI_CS', 'utf8', 3);
charsets[2162] = new Collation(2162, 'UCA1400_SPANISH2_AS_CI', 'utf8', 3);
charsets[2163] = new Collation(2163, 'UCA1400_SPANISH2_AS_CS', 'utf8', 3);
charsets[2164] = new Collation(2164, 'UCA1400_SPANISH2_NOPAD_AI_CI', 'utf8', 3);
charsets[2165] = new Collation(2165, 'UCA1400_SPANISH2_NOPAD_AI_CS', 'utf8', 3);
charsets[2166] = new Collation(2166, 'UCA1400_SPANISH2_NOPAD_AS_CI', 'utf8', 3);
charsets[2167] = new Collation(2167, 'UCA1400_SPANISH2_NOPAD_AS_CS', 'utf8', 3);
charsets[2168] = new Collation(2168, 'UCA1400_ROMAN_AI_CI', 'utf8', 3);
charsets[2169] = new Collation(2169, 'UCA1400_ROMAN_AI_CS', 'utf8', 3);
charsets[2170] = new Collation(2170, 'UCA1400_ROMAN_AS_CI', 'utf8', 3);
charsets[2171] = new Collation(2171, 'UCA1400_ROMAN_AS_CS', 'utf8', 3);
charsets[2172] = new Collation(2172, 'UCA1400_ROMAN_NOPAD_AI_CI', 'utf8', 3);
charsets[2173] = new Collation(2173, 'UCA1400_ROMAN_NOPAD_AI_CS', 'utf8', 3);
charsets[2174] = new Collation(2174, 'UCA1400_ROMAN_NOPAD_AS_CI', 'utf8', 3);
charsets[2175] = new Collation(2175, 'UCA1400_ROMAN_NOPAD_AS_CS', 'utf8', 3);
charsets[2176] = new Collation(2176, 'UCA1400_PERSIAN_AI_CI', 'utf8', 3);
charsets[2177] = new Collation(2177, 'UCA1400_PERSIAN_AI_CS', 'utf8', 3);
charsets[2178] = new Collation(2178, 'UCA1400_PERSIAN_AS_CI', 'utf8', 3);
charsets[2179] = new Collation(2179, 'UCA1400_PERSIAN_AS_CS', 'utf8', 3);
charsets[2180] = new Collation(2180, 'UCA1400_PERSIAN_NOPAD_AI_CI', 'utf8', 3);
charsets[2181] = new Collation(2181, 'UCA1400_PERSIAN_NOPAD_AI_CS', 'utf8', 3);
charsets[2182] = new Collation(2182, 'UCA1400_PERSIAN_NOPAD_AS_CI', 'utf8', 3);
charsets[2183] = new Collation(2183, 'UCA1400_PERSIAN_NOPAD_AS_CS', 'utf8', 3);
charsets[2184] = new Collation(2184, 'UCA1400_ESPERANTO_AI_CI', 'utf8', 3);
charsets[2185] = new Collation(2185, 'UCA1400_ESPERANTO_AI_CS', 'utf8', 3);
charsets[2186] = new Collation(2186, 'UCA1400_ESPERANTO_AS_CI', 'utf8', 3);
charsets[2187] = new Collation(2187, 'UCA1400_ESPERANTO_AS_CS', 'utf8', 3);
charsets[2188] = new Collation(2188, 'UCA1400_ESPERANTO_NOPAD_AI_CI', 'utf8', 3);
charsets[2189] = new Collation(2189, 'UCA1400_ESPERANTO_NOPAD_AI_CS', 'utf8', 3);
charsets[2190] = new Collation(2190, 'UCA1400_ESPERANTO_NOPAD_AS_CI', 'utf8', 3);
charsets[2191] = new Collation(2191, 'UCA1400_ESPERANTO_NOPAD_AS_CS', 'utf8', 3);
charsets[2192] = new Collation(2192, 'UCA1400_HUNGARIAN_AI_CI', 'utf8', 3);
charsets[2193] = new Collation(2193, 'UCA1400_HUNGARIAN_AI_CS', 'utf8', 3);
charsets[2194] = new Collation(2194, 'UCA1400_HUNGARIAN_AS_CI', 'utf8', 3);
charsets[2195] = new Collation(2195, 'UCA1400_HUNGARIAN_AS_CS', 'utf8', 3);
charsets[2196] = new Collation(2196, 'UCA1400_HUNGARIAN_NOPAD_AI_CI', 'utf8', 3);
charsets[2197] = new Collation(2197, 'UCA1400_HUNGARIAN_NOPAD_AI_CS', 'utf8', 3);
charsets[2198] = new Collation(2198, 'UCA1400_HUNGARIAN_NOPAD_AS_CI', 'utf8', 3);
charsets[2199] = new Collation(2199, 'UCA1400_HUNGARIAN_NOPAD_AS_CS', 'utf8', 3);
charsets[2200] = new Collation(2200, 'UCA1400_SINHALA_AI_CI', 'utf8', 3);
charsets[2201] = new Collation(2201, 'UCA1400_SINHALA_AI_CS', 'utf8', 3);
charsets[2202] = new Collation(2202, 'UCA1400_SINHALA_AS_CI', 'utf8', 3);
charsets[2203] = new Collation(2203, 'UCA1400_SINHALA_AS_CS', 'utf8', 3);
charsets[2204] = new Collation(2204, 'UCA1400_SINHALA_NOPAD_AI_CI', 'utf8', 3);
charsets[2205] = new Collation(2205, 'UCA1400_SINHALA_NOPAD_AI_CS', 'utf8', 3);
charsets[2206] = new Collation(2206, 'UCA1400_SINHALA_NOPAD_AS_CI', 'utf8', 3);
charsets[2207] = new Collation(2207, 'UCA1400_SINHALA_NOPAD_AS_CS', 'utf8', 3);
charsets[2208] = new Collation(2208, 'UCA1400_GERMAN2_AI_CI', 'utf8', 3);
charsets[2209] = new Collation(2209, 'UCA1400_GERMAN2_AI_CS', 'utf8', 3);
charsets[2210] = new Collation(2210, 'UCA1400_GERMAN2_AS_CI', 'utf8', 3);
charsets[2211] = new Collation(2211, 'UCA1400_GERMAN2_AS_CS', 'utf8', 3);
charsets[2212] = new Collation(2212, 'UCA1400_GERMAN2_NOPAD_AI_CI', 'utf8', 3);
charsets[2213] = new Collation(2213, 'UCA1400_GERMAN2_NOPAD_AI_CS', 'utf8', 3);
charsets[2214] = new Collation(2214, 'UCA1400_GERMAN2_NOPAD_AS_CI', 'utf8', 3);
charsets[2215] = new Collation(2215, 'UCA1400_GERMAN2_NOPAD_AS_CS', 'utf8', 3);
charsets[2232] = new Collation(2232, 'UCA1400_VIETNAMESE_AI_CI', 'utf8', 3);
charsets[2233] = new Collation(2233, 'UCA1400_VIETNAMESE_AI_CS', 'utf8', 3);
charsets[2234] = new Collation(2234, 'UCA1400_VIETNAMESE_AS_CI', 'utf8', 3);
charsets[2235] = new Collation(2235, 'UCA1400_VIETNAMESE_AS_CS', 'utf8', 3);
charsets[2236] = new Collation(2236, 'UCA1400_VIETNAMESE_NOPAD_AI_CI', 'utf8', 3);
charsets[2237] = new Collation(2237, 'UCA1400_VIETNAMESE_NOPAD_AI_CS', 'utf8', 3);
charsets[2238] = new Collation(2238, 'UCA1400_VIETNAMESE_NOPAD_AS_CI', 'utf8', 3);
charsets[2239] = new Collation(2239, 'UCA1400_VIETNAMESE_NOPAD_AS_CS', 'utf8', 3);
charsets[2240] = new Collation(2240, 'UCA1400_CROATIAN_AI_CI', 'utf8', 3);
charsets[2241] = new Collation(2241, 'UCA1400_CROATIAN_AI_CS', 'utf8', 3);
charsets[2242] = new Collation(2242, 'UCA1400_CROATIAN_AS_CI', 'utf8', 3);
charsets[2243] = new Collation(2243, 'UCA1400_CROATIAN_AS_CS', 'utf8', 3);
charsets[2244] = new Collation(2244, 'UCA1400_CROATIAN_NOPAD_AI_CI', 'utf8', 3);
charsets[2245] = new Collation(2245, 'UCA1400_CROATIAN_NOPAD_AI_CS', 'utf8', 3);
charsets[2246] = new Collation(2246, 'UCA1400_CROATIAN_NOPAD_AS_CI', 'utf8', 3);
charsets[2247] = new Collation(2247, 'UCA1400_CROATIAN_NOPAD_AS_CS', 'utf8', 3);
charsets[2304] = new Collation(2304, 'UCA1400_AI_CI', 'utf8', 4);
charsets[2305] = new Collation(2305, 'UCA1400_AI_CS', 'utf8', 4);
charsets[2306] = new Collation(2306, 'UCA1400_AS_CI', 'utf8', 4);
charsets[2307] = new Collation(2307, 'UCA1400_AS_CS', 'utf8', 4);
charsets[2308] = new Collation(2308, 'UCA1400_NOPAD_AI_CI', 'utf8', 4);
charsets[2309] = new Collation(2309, 'UCA1400_NOPAD_AI_CS', 'utf8', 4);
charsets[2310] = new Collation(2310, 'UCA1400_NOPAD_AS_CI', 'utf8', 4);
charsets[2311] = new Collation(2311, 'UCA1400_NOPAD_AS_CS', 'utf8', 4);
charsets[2312] = new Collation(2312, 'UCA1400_ICELANDIC_AI_CI', 'utf8', 4);
charsets[2313] = new Collation(2313, 'UCA1400_ICELANDIC_AI_CS', 'utf8', 4);
charsets[2314] = new Collation(2314, 'UCA1400_ICELANDIC_AS_CI', 'utf8', 4);
charsets[2315] = new Collation(2315, 'UCA1400_ICELANDIC_AS_CS', 'utf8', 4);
charsets[2316] = new Collation(2316, 'UCA1400_ICELANDIC_NOPAD_AI_CI', 'utf8', 4);
charsets[2317] = new Collation(2317, 'UCA1400_ICELANDIC_NOPAD_AI_CS', 'utf8', 4);
charsets[2318] = new Collation(2318, 'UCA1400_ICELANDIC_NOPAD_AS_CI', 'utf8', 4);
charsets[2319] = new Collation(2319, 'UCA1400_ICELANDIC_NOPAD_AS_CS', 'utf8', 4);
charsets[2320] = new Collation(2320, 'UCA1400_LATVIAN_AI_CI', 'utf8', 4);
charsets[2321] = new Collation(2321, 'UCA1400_LATVIAN_AI_CS', 'utf8', 4);
charsets[2322] = new Collation(2322, 'UCA1400_LATVIAN_AS_CI', 'utf8', 4);
charsets[2323] = new Collation(2323, 'UCA1400_LATVIAN_AS_CS', 'utf8', 4);
charsets[2324] = new Collation(2324, 'UCA1400_LATVIAN_NOPAD_AI_CI', 'utf8', 4);
charsets[2325] = new Collation(2325, 'UCA1400_LATVIAN_NOPAD_AI_CS', 'utf8', 4);
charsets[2326] = new Collation(2326, 'UCA1400_LATVIAN_NOPAD_AS_CI', 'utf8', 4);
charsets[2327] = new Collation(2327, 'UCA1400_LATVIAN_NOPAD_AS_CS', 'utf8', 4);
charsets[2328] = new Collation(2328, 'UCA1400_ROMANIAN_AI_CI', 'utf8', 4);
charsets[2329] = new Collation(2329, 'UCA1400_ROMANIAN_AI_CS', 'utf8', 4);
charsets[2330] = new Collation(2330, 'UCA1400_ROMANIAN_AS_CI', 'utf8', 4);
charsets[2331] = new Collation(2331, 'UCA1400_ROMANIAN_AS_CS', 'utf8', 4);
charsets[2332] = new Collation(2332, 'UCA1400_ROMANIAN_NOPAD_AI_CI', 'utf8', 4);
charsets[2333] = new Collation(2333, 'UCA1400_ROMANIAN_NOPAD_AI_CS', 'utf8', 4);
charsets[2334] = new Collation(2334, 'UCA1400_ROMANIAN_NOPAD_AS_CI', 'utf8', 4);
charsets[2335] = new Collation(2335, 'UCA1400_ROMANIAN_NOPAD_AS_CS', 'utf8', 4);
charsets[2336] = new Collation(2336, 'UCA1400_SLOVENIAN_AI_CI', 'utf8', 4);
charsets[2337] = new Collation(2337, 'UCA1400_SLOVENIAN_AI_CS', 'utf8', 4);
charsets[2338] = new Collation(2338, 'UCA1400_SLOVENIAN_AS_CI', 'utf8', 4);
charsets[2339] = new Collation(2339, 'UCA1400_SLOVENIAN_AS_CS', 'utf8', 4);
charsets[2340] = new Collation(2340, 'UCA1400_SLOVENIAN_NOPAD_AI_CI', 'utf8', 4);
charsets[2341] = new Collation(2341, 'UCA1400_SLOVENIAN_NOPAD_AI_CS', 'utf8', 4);
charsets[2342] = new Collation(2342, 'UCA1400_SLOVENIAN_NOPAD_AS_CI', 'utf8', 4);
charsets[2343] = new Collation(2343, 'UCA1400_SLOVENIAN_NOPAD_AS_CS', 'utf8', 4);
charsets[2344] = new Collation(2344, 'UCA1400_POLISH_AI_CI', 'utf8', 4);
charsets[2345] = new Collation(2345, 'UCA1400_POLISH_AI_CS', 'utf8', 4);
charsets[2346] = new Collation(2346, 'UCA1400_POLISH_AS_CI', 'utf8', 4);
charsets[2347] = new Collation(2347, 'UCA1400_POLISH_AS_CS', 'utf8', 4);
charsets[2348] = new Collation(2348, 'UCA1400_POLISH_NOPAD_AI_CI', 'utf8', 4);
charsets[2349] = new Collation(2349, 'UCA1400_POLISH_NOPAD_AI_CS', 'utf8', 4);
charsets[2350] = new Collation(2350, 'UCA1400_POLISH_NOPAD_AS_CI', 'utf8', 4);
charsets[2351] = new Collation(2351, 'UCA1400_POLISH_NOPAD_AS_CS', 'utf8', 4);
charsets[2352] = new Collation(2352, 'UCA1400_ESTONIAN_AI_CI', 'utf8', 4);
charsets[2353] = new Collation(2353, 'UCA1400_ESTONIAN_AI_CS', 'utf8', 4);
charsets[2354] = new Collation(2354, 'UCA1400_ESTONIAN_AS_CI', 'utf8', 4);
charsets[2355] = new Collation(2355, 'UCA1400_ESTONIAN_AS_CS', 'utf8', 4);
charsets[2356] = new Collation(2356, 'UCA1400_ESTONIAN_NOPAD_AI_CI', 'utf8', 4);
charsets[2357] = new Collation(2357, 'UCA1400_ESTONIAN_NOPAD_AI_CS', 'utf8', 4);
charsets[2358] = new Collation(2358, 'UCA1400_ESTONIAN_NOPAD_AS_CI', 'utf8', 4);
charsets[2359] = new Collation(2359, 'UCA1400_ESTONIAN_NOPAD_AS_CS', 'utf8', 4);
charsets[2360] = new Collation(2360, 'UCA1400_SPANISH_AI_CI', 'utf8', 4);
charsets[2361] = new Collation(2361, 'UCA1400_SPANISH_AI_CS', 'utf8', 4);
charsets[2362] = new Collation(2362, 'UCA1400_SPANISH_AS_CI', 'utf8', 4);
charsets[2363] = new Collation(2363, 'UCA1400_SPANISH_AS_CS', 'utf8', 4);
charsets[2364] = new Collation(2364, 'UCA1400_SPANISH_NOPAD_AI_CI', 'utf8', 4);
charsets[2365] = new Collation(2365, 'UCA1400_SPANISH_NOPAD_AI_CS', 'utf8', 4);
charsets[2366] = new Collation(2366, 'UCA1400_SPANISH_NOPAD_AS_CI', 'utf8', 4);
charsets[2367] = new Collation(2367, 'UCA1400_SPANISH_NOPAD_AS_CS', 'utf8', 4);
charsets[2368] = new Collation(2368, 'UCA1400_SWEDISH_AI_CI', 'utf8', 4);
charsets[2369] = new Collation(2369, 'UCA1400_SWEDISH_AI_CS', 'utf8', 4);
charsets[2370] = new Collation(2370, 'UCA1400_SWEDISH_AS_CI', 'utf8', 4);
charsets[2371] = new Collation(2371, 'UCA1400_SWEDISH_AS_CS', 'utf8', 4);
charsets[2372] = new Collation(2372, 'UCA1400_SWEDISH_NOPAD_AI_CI', 'utf8', 4);
charsets[2373] = new Collation(2373, 'UCA1400_SWEDISH_NOPAD_AI_CS', 'utf8', 4);
charsets[2374] = new Collation(2374, 'UCA1400_SWEDISH_NOPAD_AS_CI', 'utf8', 4);
charsets[2375] = new Collation(2375, 'UCA1400_SWEDISH_NOPAD_AS_CS', 'utf8', 4);
charsets[2376] = new Collation(2376, 'UCA1400_TURKISH_AI_CI', 'utf8', 4);
charsets[2377] = new Collation(2377, 'UCA1400_TURKISH_AI_CS', 'utf8', 4);
charsets[2378] = new Collation(2378, 'UCA1400_TURKISH_AS_CI', 'utf8', 4);
charsets[2379] = new Collation(2379, 'UCA1400_TURKISH_AS_CS', 'utf8', 4);
charsets[2380] = new Collation(2380, 'UCA1400_TURKISH_NOPAD_AI_CI', 'utf8', 4);
charsets[2381] = new Collation(2381, 'UCA1400_TURKISH_NOPAD_AI_CS', 'utf8', 4);
charsets[2382] = new Collation(2382, 'UCA1400_TURKISH_NOPAD_AS_CI', 'utf8', 4);
charsets[2383] = new Collation(2383, 'UCA1400_TURKISH_NOPAD_AS_CS', 'utf8', 4);
charsets[2384] = new Collation(2384, 'UCA1400_CZECH_AI_CI', 'utf8', 4);
charsets[2385] = new Collation(2385, 'UCA1400_CZECH_AI_CS', 'utf8', 4);
charsets[2386] = new Collation(2386, 'UCA1400_CZECH_AS_CI', 'utf8', 4);
charsets[2387] = new Collation(2387, 'UCA1400_CZECH_AS_CS', 'utf8', 4);
charsets[2388] = new Collation(2388, 'UCA1400_CZECH_NOPAD_AI_CI', 'utf8', 4);
charsets[2389] = new Collation(2389, 'UCA1400_CZECH_NOPAD_AI_CS', 'utf8', 4);
charsets[2390] = new Collation(2390, 'UCA1400_CZECH_NOPAD_AS_CI', 'utf8', 4);
charsets[2391] = new Collation(2391, 'UCA1400_CZECH_NOPAD_AS_CS', 'utf8', 4);
charsets[2392] = new Collation(2392, 'UCA1400_DANISH_AI_CI', 'utf8', 4);
charsets[2393] = new Collation(2393, 'UCA1400_DANISH_AI_CS', 'utf8', 4);
charsets[2394] = new Collation(2394, 'UCA1400_DANISH_AS_CI', 'utf8', 4);
charsets[2395] = new Collation(2395, 'UCA1400_DANISH_AS_CS', 'utf8', 4);
charsets[2396] = new Collation(2396, 'UCA1400_DANISH_NOPAD_AI_CI', 'utf8', 4);
charsets[2397] = new Collation(2397, 'UCA1400_DANISH_NOPAD_AI_CS', 'utf8', 4);
charsets[2398] = new Collation(2398, 'UCA1400_DANISH_NOPAD_AS_CI', 'utf8', 4);
charsets[2399] = new Collation(2399, 'UCA1400_DANISH_NOPAD_AS_CS', 'utf8', 4);
charsets[2400] = new Collation(2400, 'UCA1400_LITHUANIAN_AI_CI', 'utf8', 4);
charsets[2401] = new Collation(2401, 'UCA1400_LITHUANIAN_AI_CS', 'utf8', 4);
charsets[2402] = new Collation(2402, 'UCA1400_LITHUANIAN_AS_CI', 'utf8', 4);
charsets[2403] = new Collation(2403, 'UCA1400_LITHUANIAN_AS_CS', 'utf8', 4);
charsets[2404] = new Collation(2404, 'UCA1400_LITHUANIAN_NOPAD_AI_CI', 'utf8', 4);
charsets[2405] = new Collation(2405, 'UCA1400_LITHUANIAN_NOPAD_AI_CS', 'utf8', 4);
charsets[2406] = new Collation(2406, 'UCA1400_LITHUANIAN_NOPAD_AS_CI', 'utf8', 4);
charsets[2407] = new Collation(2407, 'UCA1400_LITHUANIAN_NOPAD_AS_CS', 'utf8', 4);
charsets[2408] = new Collation(2408, 'UCA1400_SLOVAK_AI_CI', 'utf8', 4);
charsets[2409] = new Collation(2409, 'UCA1400_SLOVAK_AI_CS', 'utf8', 4);
charsets[2410] = new Collation(2410, 'UCA1400_SLOVAK_AS_CI', 'utf8', 4);
charsets[2411] = new Collation(2411, 'UCA1400_SLOVAK_AS_CS', 'utf8', 4);
charsets[2412] = new Collation(2412, 'UCA1400_SLOVAK_NOPAD_AI_CI', 'utf8', 4);
charsets[2413] = new Collation(2413, 'UCA1400_SLOVAK_NOPAD_AI_CS', 'utf8', 4);
charsets[2414] = new Collation(2414, 'UCA1400_SLOVAK_NOPAD_AS_CI', 'utf8', 4);
charsets[2415] = new Collation(2415, 'UCA1400_SLOVAK_NOPAD_AS_CS', 'utf8', 4);
charsets[2416] = new Collation(2416, 'UCA1400_SPANISH2_AI_CI', 'utf8', 4);
charsets[2417] = new Collation(2417, 'UCA1400_SPANISH2_AI_CS', 'utf8', 4);
charsets[2418] = new Collation(2418, 'UCA1400_SPANISH2_AS_CI', 'utf8', 4);
charsets[2419] = new Collation(2419, 'UCA1400_SPANISH2_AS_CS', 'utf8', 4);
charsets[2420] = new Collation(2420, 'UCA1400_SPANISH2_NOPAD_AI_CI', 'utf8', 4);
charsets[2421] = new Collation(2421, 'UCA1400_SPANISH2_NOPAD_AI_CS', 'utf8', 4);
charsets[2422] = new Collation(2422, 'UCA1400_SPANISH2_NOPAD_AS_CI', 'utf8', 4);
charsets[2423] = new Collation(2423, 'UCA1400_SPANISH2_NOPAD_AS_CS', 'utf8', 4);
charsets[2424] = new Collation(2424, 'UCA1400_ROMAN_AI_CI', 'utf8', 4);
charsets[2425] = new Collation(2425, 'UCA1400_ROMAN_AI_CS', 'utf8', 4);
charsets[2426] = new Collation(2426, 'UCA1400_ROMAN_AS_CI', 'utf8', 4);
charsets[2427] = new Collation(2427, 'UCA1400_ROMAN_AS_CS', 'utf8', 4);
charsets[2428] = new Collation(2428, 'UCA1400_ROMAN_NOPAD_AI_CI', 'utf8', 4);
charsets[2429] = new Collation(2429, 'UCA1400_ROMAN_NOPAD_AI_CS', 'utf8', 4);
charsets[2430] = new Collation(2430, 'UCA1400_ROMAN_NOPAD_AS_CI', 'utf8', 4);
charsets[2431] = new Collation(2431, 'UCA1400_ROMAN_NOPAD_AS_CS', 'utf8', 4);
charsets[2432] = new Collation(2432, 'UCA1400_PERSIAN_AI_CI', 'utf8', 4);
charsets[2433] = new Collation(2433, 'UCA1400_PERSIAN_AI_CS', 'utf8', 4);
charsets[2434] = new Collation(2434, 'UCA1400_PERSIAN_AS_CI', 'utf8', 4);
charsets[2435] = new Collation(2435, 'UCA1400_PERSIAN_AS_CS', 'utf8', 4);
charsets[2436] = new Collation(2436, 'UCA1400_PERSIAN_NOPAD_AI_CI', 'utf8', 4);
charsets[2437] = new Collation(2437, 'UCA1400_PERSIAN_NOPAD_AI_CS', 'utf8', 4);
charsets[2438] = new Collation(2438, 'UCA1400_PERSIAN_NOPAD_AS_CI', 'utf8', 4);
charsets[2439] = new Collation(2439, 'UCA1400_PERSIAN_NOPAD_AS_CS', 'utf8', 4);
charsets[2440] = new Collation(2440, 'UCA1400_ESPERANTO_AI_CI', 'utf8', 4);
charsets[2441] = new Collation(2441, 'UCA1400_ESPERANTO_AI_CS', 'utf8', 4);
charsets[2442] = new Collation(2442, 'UCA1400_ESPERANTO_AS_CI', 'utf8', 4);
charsets[2443] = new Collation(2443, 'UCA1400_ESPERANTO_AS_CS', 'utf8', 4);
charsets[2444] = new Collation(2444, 'UCA1400_ESPERANTO_NOPAD_AI_CI', 'utf8', 4);
charsets[2445] = new Collation(2445, 'UCA1400_ESPERANTO_NOPAD_AI_CS', 'utf8', 4);
charsets[2446] = new Collation(2446, 'UCA1400_ESPERANTO_NOPAD_AS_CI', 'utf8', 4);
charsets[2447] = new Collation(2447, 'UCA1400_ESPERANTO_NOPAD_AS_CS', 'utf8', 4);
charsets[2448] = new Collation(2448, 'UCA1400_HUNGARIAN_AI_CI', 'utf8', 4);
charsets[2449] = new Collation(2449, 'UCA1400_HUNGARIAN_AI_CS', 'utf8', 4);
charsets[2450] = new Collation(2450, 'UCA1400_HUNGARIAN_AS_CI', 'utf8', 4);
charsets[2451] = new Collation(2451, 'UCA1400_HUNGARIAN_AS_CS', 'utf8', 4);
charsets[2452] = new Collation(2452, 'UCA1400_HUNGARIAN_NOPAD_AI_CI', 'utf8', 4);
charsets[2453] = new Collation(2453, 'UCA1400_HUNGARIAN_NOPAD_AI_CS', 'utf8', 4);
charsets[2454] = new Collation(2454, 'UCA1400_HUNGARIAN_NOPAD_AS_CI', 'utf8', 4);
charsets[2455] = new Collation(2455, 'UCA1400_HUNGARIAN_NOPAD_AS_CS', 'utf8', 4);
charsets[2456] = new Collation(2456, 'UCA1400_SINHALA_AI_CI', 'utf8', 4);
charsets[2457] = new Collation(2457, 'UCA1400_SINHALA_AI_CS', 'utf8', 4);
charsets[2458] = new Collation(2458, 'UCA1400_SINHALA_AS_CI', 'utf8', 4);
charsets[2459] = new Collation(2459, 'UCA1400_SINHALA_AS_CS', 'utf8', 4);
charsets[2460] = new Collation(2460, 'UCA1400_SINHALA_NOPAD_AI_CI', 'utf8', 4);
charsets[2461] = new Collation(2461, 'UCA1400_SINHALA_NOPAD_AI_CS', 'utf8', 4);
charsets[2462] = new Collation(2462, 'UCA1400_SINHALA_NOPAD_AS_CI', 'utf8', 4);
charsets[2463] = new Collation(2463, 'UCA1400_SINHALA_NOPAD_AS_CS', 'utf8', 4);
charsets[2464] = new Collation(2464, 'UCA1400_GERMAN2_AI_CI', 'utf8', 4);
charsets[2465] = new Collation(2465, 'UCA1400_GERMAN2_AI_CS', 'utf8', 4);
charsets[2466] = new Collation(2466, 'UCA1400_GERMAN2_AS_CI', 'utf8', 4);
charsets[2467] = new Collation(2467, 'UCA1400_GERMAN2_AS_CS', 'utf8', 4);
charsets[2468] = new Collation(2468, 'UCA1400_GERMAN2_NOPAD_AI_CI', 'utf8', 4);
charsets[2469] = new Collation(2469, 'UCA1400_GERMAN2_NOPAD_AI_CS', 'utf8', 4);
charsets[2470] = new Collation(2470, 'UCA1400_GERMAN2_NOPAD_AS_CI', 'utf8', 4);
charsets[2471] = new Collation(2471, 'UCA1400_GERMAN2_NOPAD_AS_CS', 'utf8', 4);
charsets[2488] = new Collation(2488, 'UCA1400_VIETNAMESE_AI_CI', 'utf8', 4);
charsets[2489] = new Collation(2489, 'UCA1400_VIETNAMESE_AI_CS', 'utf8', 4);
charsets[2490] = new Collation(2490, 'UCA1400_VIETNAMESE_AS_CI', 'utf8', 4);
charsets[2491] = new Collation(2491, 'UCA1400_VIETNAMESE_AS_CS', 'utf8', 4);
charsets[2492] = new Collation(2492, 'UCA1400_VIETNAMESE_NOPAD_AI_CI', 'utf8', 4);
charsets[2493] = new Collation(2493, 'UCA1400_VIETNAMESE_NOPAD_AI_CS', 'utf8', 4);
charsets[2494] = new Collation(2494, 'UCA1400_VIETNAMESE_NOPAD_AS_CI', 'utf8', 4);
charsets[2495] = new Collation(2495, 'UCA1400_VIETNAMESE_NOPAD_AS_CS', 'utf8', 4);
charsets[2496] = new Collation(2496, 'UCA1400_CROATIAN_AI_CI', 'utf8', 4);
charsets[2497] = new Collation(2497, 'UCA1400_CROATIAN_AI_CS', 'utf8', 4);
charsets[2498] = new Collation(2498, 'UCA1400_CROATIAN_AS_CI', 'utf8', 4);
charsets[2499] = new Collation(2499, 'UCA1400_CROATIAN_AS_CS', 'utf8', 4);
charsets[2500] = new Collation(2500, 'UCA1400_CROATIAN_NOPAD_AI_CI', 'utf8', 4);
charsets[2501] = new Collation(2501, 'UCA1400_CROATIAN_NOPAD_AI_CS', 'utf8', 4);
charsets[2502] = new Collation(2502, 'UCA1400_CROATIAN_NOPAD_AS_CI', 'utf8', 4);
charsets[2503] = new Collation(2503, 'UCA1400_CROATIAN_NOPAD_AS_CS', 'utf8', 4);
charsets[2560] = new Collation(2560, 'UCA1400_AI_CI', 'ucs2', 2);
charsets[2561] = new Collation(2561, 'UCA1400_AI_CS', 'ucs2', 2);
charsets[2562] = new Collation(2562, 'UCA1400_AS_CI', 'ucs2', 2);
charsets[2563] = new Collation(2563, 'UCA1400_AS_CS', 'ucs2', 2);
charsets[2564] = new Collation(2564, 'UCA1400_NOPAD_AI_CI', 'ucs2', 2);
charsets[2565] = new Collation(2565, 'UCA1400_NOPAD_AI_CS', 'ucs2', 2);
charsets[2566] = new Collation(2566, 'UCA1400_NOPAD_AS_CI', 'ucs2', 2);
charsets[2567] = new Collation(2567, 'UCA1400_NOPAD_AS_CS', 'ucs2', 2);
charsets[2568] = new Collation(2568, 'UCA1400_ICELANDIC_AI_CI', 'ucs2', 2);
charsets[2569] = new Collation(2569, 'UCA1400_ICELANDIC_AI_CS', 'ucs2', 2);
charsets[2570] = new Collation(2570, 'UCA1400_ICELANDIC_AS_CI', 'ucs2', 2);
charsets[2571] = new Collation(2571, 'UCA1400_ICELANDIC_AS_CS', 'ucs2', 2);
charsets[2572] = new Collation(2572, 'UCA1400_ICELANDIC_NOPAD_AI_CI', 'ucs2', 2);
charsets[2573] = new Collation(2573, 'UCA1400_ICELANDIC_NOPAD_AI_CS', 'ucs2', 2);
charsets[2574] = new Collation(2574, 'UCA1400_ICELANDIC_NOPAD_AS_CI', 'ucs2', 2);
charsets[2575] = new Collation(2575, 'UCA1400_ICELANDIC_NOPAD_AS_CS', 'ucs2', 2);
charsets[2576] = new Collation(2576, 'UCA1400_LATVIAN_AI_CI', 'ucs2', 2);
charsets[2577] = new Collation(2577, 'UCA1400_LATVIAN_AI_CS', 'ucs2', 2);
charsets[2578] = new Collation(2578, 'UCA1400_LATVIAN_AS_CI', 'ucs2', 2);
charsets[2579] = new Collation(2579, 'UCA1400_LATVIAN_AS_CS', 'ucs2', 2);
charsets[2580] = new Collation(2580, 'UCA1400_LATVIAN_NOPAD_AI_CI', 'ucs2', 2);
charsets[2581] = new Collation(2581, 'UCA1400_LATVIAN_NOPAD_AI_CS', 'ucs2', 2);
charsets[2582] = new Collation(2582, 'UCA1400_LATVIAN_NOPAD_AS_CI', 'ucs2', 2);
charsets[2583] = new Collation(2583, 'UCA1400_LATVIAN_NOPAD_AS_CS', 'ucs2', 2);
charsets[2584] = new Collation(2584, 'UCA1400_ROMANIAN_AI_CI', 'ucs2', 2);
charsets[2585] = new Collation(2585, 'UCA1400_ROMANIAN_AI_CS', 'ucs2', 2);
charsets[2586] = new Collation(2586, 'UCA1400_ROMANIAN_AS_CI', 'ucs2', 2);
charsets[2587] = new Collation(2587, 'UCA1400_ROMANIAN_AS_CS', 'ucs2', 2);
charsets[2588] = new Collation(2588, 'UCA1400_ROMANIAN_NOPAD_AI_CI', 'ucs2', 2);
charsets[2589] = new Collation(2589, 'UCA1400_ROMANIAN_NOPAD_AI_CS', 'ucs2', 2);
charsets[2590] = new Collation(2590, 'UCA1400_ROMANIAN_NOPAD_AS_CI', 'ucs2', 2);
charsets[2591] = new Collation(2591, 'UCA1400_ROMANIAN_NOPAD_AS_CS', 'ucs2', 2);
charsets[2592] = new Collation(2592, 'UCA1400_SLOVENIAN_AI_CI', 'ucs2', 2);
charsets[2593] = new Collation(2593, 'UCA1400_SLOVENIAN_AI_CS', 'ucs2', 2);
charsets[2594] = new Collation(2594, 'UCA1400_SLOVENIAN_AS_CI', 'ucs2', 2);
charsets[2595] = new Collation(2595, 'UCA1400_SLOVENIAN_AS_CS', 'ucs2', 2);
charsets[2596] = new Collation(2596, 'UCA1400_SLOVENIAN_NOPAD_AI_CI', 'ucs2', 2);
charsets[2597] = new Collation(2597, 'UCA1400_SLOVENIAN_NOPAD_AI_CS', 'ucs2', 2);
charsets[2598] = new Collation(2598, 'UCA1400_SLOVENIAN_NOPAD_AS_CI', 'ucs2', 2);
charsets[2599] = new Collation(2599, 'UCA1400_SLOVENIAN_NOPAD_AS_CS', 'ucs2', 2);
charsets[2600] = new Collation(2600, 'UCA1400_POLISH_AI_CI', 'ucs2', 2);
charsets[2601] = new Collation(2601, 'UCA1400_POLISH_AI_CS', 'ucs2', 2);
charsets[2602] = new Collation(2602, 'UCA1400_POLISH_AS_CI', 'ucs2', 2);
charsets[2603] = new Collation(2603, 'UCA1400_POLISH_AS_CS', 'ucs2', 2);
charsets[2604] = new Collation(2604, 'UCA1400_POLISH_NOPAD_AI_CI', 'ucs2', 2);
charsets[2605] = new Collation(2605, 'UCA1400_POLISH_NOPAD_AI_CS', 'ucs2', 2);
charsets[2606] = new Collation(2606, 'UCA1400_POLISH_NOPAD_AS_CI', 'ucs2', 2);
charsets[2607] = new Collation(2607, 'UCA1400_POLISH_NOPAD_AS_CS', 'ucs2', 2);
charsets[2608] = new Collation(2608, 'UCA1400_ESTONIAN_AI_CI', 'ucs2', 2);
charsets[2609] = new Collation(2609, 'UCA1400_ESTONIAN_AI_CS', 'ucs2', 2);
charsets[2610] = new Collation(2610, 'UCA1400_ESTONIAN_AS_CI', 'ucs2', 2);
charsets[2611] = new Collation(2611, 'UCA1400_ESTONIAN_AS_CS', 'ucs2', 2);
charsets[2612] = new Collation(2612, 'UCA1400_ESTONIAN_NOPAD_AI_CI', 'ucs2', 2);
charsets[2613] = new Collation(2613, 'UCA1400_ESTONIAN_NOPAD_AI_CS', 'ucs2', 2);
charsets[2614] = new Collation(2614, 'UCA1400_ESTONIAN_NOPAD_AS_CI', 'ucs2', 2);
charsets[2615] = new Collation(2615, 'UCA1400_ESTONIAN_NOPAD_AS_CS', 'ucs2', 2);
charsets[2616] = new Collation(2616, 'UCA1400_SPANISH_AI_CI', 'ucs2', 2);
charsets[2617] = new Collation(2617, 'UCA1400_SPANISH_AI_CS', 'ucs2', 2);
charsets[2618] = new Collation(2618, 'UCA1400_SPANISH_AS_CI', 'ucs2', 2);
charsets[2619] = new Collation(2619, 'UCA1400_SPANISH_AS_CS', 'ucs2', 2);
charsets[2620] = new Collation(2620, 'UCA1400_SPANISH_NOPAD_AI_CI', 'ucs2', 2);
charsets[2621] = new Collation(2621, 'UCA1400_SPANISH_NOPAD_AI_CS', 'ucs2', 2);
charsets[2622] = new Collation(2622, 'UCA1400_SPANISH_NOPAD_AS_CI', 'ucs2', 2);
charsets[2623] = new Collation(2623, 'UCA1400_SPANISH_NOPAD_AS_CS', 'ucs2', 2);
charsets[2624] = new Collation(2624, 'UCA1400_SWEDISH_AI_CI', 'ucs2', 2);
charsets[2625] = new Collation(2625, 'UCA1400_SWEDISH_AI_CS', 'ucs2', 2);
charsets[2626] = new Collation(2626, 'UCA1400_SWEDISH_AS_CI', 'ucs2', 2);
charsets[2627] = new Collation(2627, 'UCA1400_SWEDISH_AS_CS', 'ucs2', 2);
charsets[2628] = new Collation(2628, 'UCA1400_SWEDISH_NOPAD_AI_CI', 'ucs2', 2);
charsets[2629] = new Collation(2629, 'UCA1400_SWEDISH_NOPAD_AI_CS', 'ucs2', 2);
charsets[2630] = new Collation(2630, 'UCA1400_SWEDISH_NOPAD_AS_CI', 'ucs2', 2);
charsets[2631] = new Collation(2631, 'UCA1400_SWEDISH_NOPAD_AS_CS', 'ucs2', 2);
charsets[2632] = new Collation(2632, 'UCA1400_TURKISH_AI_CI', 'ucs2', 2);
charsets[2633] = new Collation(2633, 'UCA1400_TURKISH_AI_CS', 'ucs2', 2);
charsets[2634] = new Collation(2634, 'UCA1400_TURKISH_AS_CI', 'ucs2', 2);
charsets[2635] = new Collation(2635, 'UCA1400_TURKISH_AS_CS', 'ucs2', 2);
charsets[2636] = new Collation(2636, 'UCA1400_TURKISH_NOPAD_AI_CI', 'ucs2', 2);
charsets[2637] = new Collation(2637, 'UCA1400_TURKISH_NOPAD_AI_CS', 'ucs2', 2);
charsets[2638] = new Collation(2638, 'UCA1400_TURKISH_NOPAD_AS_CI', 'ucs2', 2);
charsets[2639] = new Collation(2639, 'UCA1400_TURKISH_NOPAD_AS_CS', 'ucs2', 2);
charsets[2640] = new Collation(2640, 'UCA1400_CZECH_AI_CI', 'ucs2', 2);
charsets[2641] = new Collation(2641, 'UCA1400_CZECH_AI_CS', 'ucs2', 2);
charsets[2642] = new Collation(2642, 'UCA1400_CZECH_AS_CI', 'ucs2', 2);
charsets[2643] = new Collation(2643, 'UCA1400_CZECH_AS_CS', 'ucs2', 2);
charsets[2644] = new Collation(2644, 'UCA1400_CZECH_NOPAD_AI_CI', 'ucs2', 2);
charsets[2645] = new Collation(2645, 'UCA1400_CZECH_NOPAD_AI_CS', 'ucs2', 2);
charsets[2646] = new Collation(2646, 'UCA1400_CZECH_NOPAD_AS_CI', 'ucs2', 2);
charsets[2647] = new Collation(2647, 'UCA1400_CZECH_NOPAD_AS_CS', 'ucs2', 2);
charsets[2648] = new Collation(2648, 'UCA1400_DANISH_AI_CI', 'ucs2', 2);
charsets[2649] = new Collation(2649, 'UCA1400_DANISH_AI_CS', 'ucs2', 2);
charsets[2650] = new Collation(2650, 'UCA1400_DANISH_AS_CI', 'ucs2', 2);
charsets[2651] = new Collation(2651, 'UCA1400_DANISH_AS_CS', 'ucs2', 2);
charsets[2652] = new Collation(2652, 'UCA1400_DANISH_NOPAD_AI_CI', 'ucs2', 2);
charsets[2653] = new Collation(2653, 'UCA1400_DANISH_NOPAD_AI_CS', 'ucs2', 2);
charsets[2654] = new Collation(2654, 'UCA1400_DANISH_NOPAD_AS_CI', 'ucs2', 2);
charsets[2655] = new Collation(2655, 'UCA1400_DANISH_NOPAD_AS_CS', 'ucs2', 2);
charsets[2656] = new Collation(2656, 'UCA1400_LITHUANIAN_AI_CI', 'ucs2', 2);
charsets[2657] = new Collation(2657, 'UCA1400_LITHUANIAN_AI_CS', 'ucs2', 2);
charsets[2658] = new Collation(2658, 'UCA1400_LITHUANIAN_AS_CI', 'ucs2', 2);
charsets[2659] = new Collation(2659, 'UCA1400_LITHUANIAN_AS_CS', 'ucs2', 2);
charsets[2660] = new Collation(2660, 'UCA1400_LITHUANIAN_NOPAD_AI_CI', 'ucs2', 2);
charsets[2661] = new Collation(2661, 'UCA1400_LITHUANIAN_NOPAD_AI_CS', 'ucs2', 2);
charsets[2662] = new Collation(2662, 'UCA1400_LITHUANIAN_NOPAD_AS_CI', 'ucs2', 2);
charsets[2663] = new Collation(2663, 'UCA1400_LITHUANIAN_NOPAD_AS_CS', 'ucs2', 2);
charsets[2664] = new Collation(2664, 'UCA1400_SLOVAK_AI_CI', 'ucs2', 2);
charsets[2665] = new Collation(2665, 'UCA1400_SLOVAK_AI_CS', 'ucs2', 2);
charsets[2666] = new Collation(2666, 'UCA1400_SLOVAK_AS_CI', 'ucs2', 2);
charsets[2667] = new Collation(2667, 'UCA1400_SLOVAK_AS_CS', 'ucs2', 2);
charsets[2668] = new Collation(2668, 'UCA1400_SLOVAK_NOPAD_AI_CI', 'ucs2', 2);
charsets[2669] = new Collation(2669, 'UCA1400_SLOVAK_NOPAD_AI_CS', 'ucs2', 2);
charsets[2670] = new Collation(2670, 'UCA1400_SLOVAK_NOPAD_AS_CI', 'ucs2', 2);
charsets[2671] = new Collation(2671, 'UCA1400_SLOVAK_NOPAD_AS_CS', 'ucs2', 2);
charsets[2672] = new Collation(2672, 'UCA1400_SPANISH2_AI_CI', 'ucs2', 2);
charsets[2673] = new Collation(2673, 'UCA1400_SPANISH2_AI_CS', 'ucs2', 2);
charsets[2674] = new Collation(2674, 'UCA1400_SPANISH2_AS_CI', 'ucs2', 2);
charsets[2675] = new Collation(2675, 'UCA1400_SPANISH2_AS_CS', 'ucs2', 2);
charsets[2676] = new Collation(2676, 'UCA1400_SPANISH2_NOPAD_AI_CI', 'ucs2', 2);
charsets[2677] = new Collation(2677, 'UCA1400_SPANISH2_NOPAD_AI_CS', 'ucs2', 2);
charsets[2678] = new Collation(2678, 'UCA1400_SPANISH2_NOPAD_AS_CI', 'ucs2', 2);
charsets[2679] = new Collation(2679, 'UCA1400_SPANISH2_NOPAD_AS_CS', 'ucs2', 2);
charsets[2680] = new Collation(2680, 'UCA1400_ROMAN_AI_CI', 'ucs2', 2);
charsets[2681] = new Collation(2681, 'UCA1400_ROMAN_AI_CS', 'ucs2', 2);
charsets[2682] = new Collation(2682, 'UCA1400_ROMAN_AS_CI', 'ucs2', 2);
charsets[2683] = new Collation(2683, 'UCA1400_ROMAN_AS_CS', 'ucs2', 2);
charsets[2684] = new Collation(2684, 'UCA1400_ROMAN_NOPAD_AI_CI', 'ucs2', 2);
charsets[2685] = new Collation(2685, 'UCA1400_ROMAN_NOPAD_AI_CS', 'ucs2', 2);
charsets[2686] = new Collation(2686, 'UCA1400_ROMAN_NOPAD_AS_CI', 'ucs2', 2);
charsets[2687] = new Collation(2687, 'UCA1400_ROMAN_NOPAD_AS_CS', 'ucs2', 2);
charsets[2688] = new Collation(2688, 'UCA1400_PERSIAN_AI_CI', 'ucs2', 2);
charsets[2689] = new Collation(2689, 'UCA1400_PERSIAN_AI_CS', 'ucs2', 2);
charsets[2690] = new Collation(2690, 'UCA1400_PERSIAN_AS_CI', 'ucs2', 2);
charsets[2691] = new Collation(2691, 'UCA1400_PERSIAN_AS_CS', 'ucs2', 2);
charsets[2692] = new Collation(2692, 'UCA1400_PERSIAN_NOPAD_AI_CI', 'ucs2', 2);
charsets[2693] = new Collation(2693, 'UCA1400_PERSIAN_NOPAD_AI_CS', 'ucs2', 2);
charsets[2694] = new Collation(2694, 'UCA1400_PERSIAN_NOPAD_AS_CI', 'ucs2', 2);
charsets[2695] = new Collation(2695, 'UCA1400_PERSIAN_NOPAD_AS_CS', 'ucs2', 2);
charsets[2696] = new Collation(2696, 'UCA1400_ESPERANTO_AI_CI', 'ucs2', 2);
charsets[2697] = new Collation(2697, 'UCA1400_ESPERANTO_AI_CS', 'ucs2', 2);
charsets[2698] = new Collation(2698, 'UCA1400_ESPERANTO_AS_CI', 'ucs2', 2);
charsets[2699] = new Collation(2699, 'UCA1400_ESPERANTO_AS_CS', 'ucs2', 2);
charsets[2700] = new Collation(2700, 'UCA1400_ESPERANTO_NOPAD_AI_CI', 'ucs2', 2);
charsets[2701] = new Collation(2701, 'UCA1400_ESPERANTO_NOPAD_AI_CS', 'ucs2', 2);
charsets[2702] = new Collation(2702, 'UCA1400_ESPERANTO_NOPAD_AS_CI', 'ucs2', 2);
charsets[2703] = new Collation(2703, 'UCA1400_ESPERANTO_NOPAD_AS_CS', 'ucs2', 2);
charsets[2704] = new Collation(2704, 'UCA1400_HUNGARIAN_AI_CI', 'ucs2', 2);
charsets[2705] = new Collation(2705, 'UCA1400_HUNGARIAN_AI_CS', 'ucs2', 2);
charsets[2706] = new Collation(2706, 'UCA1400_HUNGARIAN_AS_CI', 'ucs2', 2);
charsets[2707] = new Collation(2707, 'UCA1400_HUNGARIAN_AS_CS', 'ucs2', 2);
charsets[2708] = new Collation(2708, 'UCA1400_HUNGARIAN_NOPAD_AI_CI', 'ucs2', 2);
charsets[2709] = new Collation(2709, 'UCA1400_HUNGARIAN_NOPAD_AI_CS', 'ucs2', 2);
charsets[2710] = new Collation(2710, 'UCA1400_HUNGARIAN_NOPAD_AS_CI', 'ucs2', 2);
charsets[2711] = new Collation(2711, 'UCA1400_HUNGARIAN_NOPAD_AS_CS', 'ucs2', 2);
charsets[2712] = new Collation(2712, 'UCA1400_SINHALA_AI_CI', 'ucs2', 2);
charsets[2713] = new Collation(2713, 'UCA1400_SINHALA_AI_CS', 'ucs2', 2);
charsets[2714] = new Collation(2714, 'UCA1400_SINHALA_AS_CI', 'ucs2', 2);
charsets[2715] = new Collation(2715, 'UCA1400_SINHALA_AS_CS', 'ucs2', 2);
charsets[2716] = new Collation(2716, 'UCA1400_SINHALA_NOPAD_AI_CI', 'ucs2', 2);
charsets[2717] = new Collation(2717, 'UCA1400_SINHALA_NOPAD_AI_CS', 'ucs2', 2);
charsets[2718] = new Collation(2718, 'UCA1400_SINHALA_NOPAD_AS_CI', 'ucs2', 2);
charsets[2719] = new Collation(2719, 'UCA1400_SINHALA_NOPAD_AS_CS', 'ucs2', 2);
charsets[2720] = new Collation(2720, 'UCA1400_GERMAN2_AI_CI', 'ucs2', 2);
charsets[2721] = new Collation(2721, 'UCA1400_GERMAN2_AI_CS', 'ucs2', 2);
charsets[2722] = new Collation(2722, 'UCA1400_GERMAN2_AS_CI', 'ucs2', 2);
charsets[2723] = new Collation(2723, 'UCA1400_GERMAN2_AS_CS', 'ucs2', 2);
charsets[2724] = new Collation(2724, 'UCA1400_GERMAN2_NOPAD_AI_CI', 'ucs2', 2);
charsets[2725] = new Collation(2725, 'UCA1400_GERMAN2_NOPAD_AI_CS', 'ucs2', 2);
charsets[2726] = new Collation(2726, 'UCA1400_GERMAN2_NOPAD_AS_CI', 'ucs2', 2);
charsets[2727] = new Collation(2727, 'UCA1400_GERMAN2_NOPAD_AS_CS', 'ucs2', 2);
charsets[2744] = new Collation(2744, 'UCA1400_VIETNAMESE_AI_CI', 'ucs2', 2);
charsets[2745] = new Collation(2745, 'UCA1400_VIETNAMESE_AI_CS', 'ucs2', 2);
charsets[2746] = new Collation(2746, 'UCA1400_VIETNAMESE_AS_CI', 'ucs2', 2);
charsets[2747] = new Collation(2747, 'UCA1400_VIETNAMESE_AS_CS', 'ucs2', 2);
charsets[2748] = new Collation(2748, 'UCA1400_VIETNAMESE_NOPAD_AI_CI', 'ucs2', 2);
charsets[2749] = new Collation(2749, 'UCA1400_VIETNAMESE_NOPAD_AI_CS', 'ucs2', 2);
charsets[2750] = new Collation(2750, 'UCA1400_VIETNAMESE_NOPAD_AS_CI', 'ucs2', 2);
charsets[2751] = new Collation(2751, 'UCA1400_VIETNAMESE_NOPAD_AS_CS', 'ucs2', 2);
charsets[2752] = new Collation(2752, 'UCA1400_CROATIAN_AI_CI', 'ucs2', 2);
charsets[2753] = new Collation(2753, 'UCA1400_CROATIAN_AI_CS', 'ucs2', 2);
charsets[2754] = new Collation(2754, 'UCA1400_CROATIAN_AS_CI', 'ucs2', 2);
charsets[2755] = new Collation(2755, 'UCA1400_CROATIAN_AS_CS', 'ucs2', 2);
charsets[2756] = new Collation(2756, 'UCA1400_CROATIAN_NOPAD_AI_CI', 'ucs2', 2);
charsets[2757] = new Collation(2757, 'UCA1400_CROATIAN_NOPAD_AI_CS', 'ucs2', 2);
charsets[2758] = new Collation(2758, 'UCA1400_CROATIAN_NOPAD_AS_CI', 'ucs2', 2);
charsets[2759] = new Collation(2759, 'UCA1400_CROATIAN_NOPAD_AS_CS', 'ucs2', 2);
charsets[2816] = new Collation(2816, 'UCA1400_AI_CI', 'utf16', 4);
charsets[2817] = new Collation(2817, 'UCA1400_AI_CS', 'utf16', 4);
charsets[2818] = new Collation(2818, 'UCA1400_AS_CI', 'utf16', 4);
charsets[2819] = new Collation(2819, 'UCA1400_AS_CS', 'utf16', 4);
charsets[2820] = new Collation(2820, 'UCA1400_NOPAD_AI_CI', 'utf16', 4);
charsets[2821] = new Collation(2821, 'UCA1400_NOPAD_AI_CS', 'utf16', 4);
charsets[2822] = new Collation(2822, 'UCA1400_NOPAD_AS_CI', 'utf16', 4);
charsets[2823] = new Collation(2823, 'UCA1400_NOPAD_AS_CS', 'utf16', 4);
charsets[2824] = new Collation(2824, 'UCA1400_ICELANDIC_AI_CI', 'utf16', 4);
charsets[2825] = new Collation(2825, 'UCA1400_ICELANDIC_AI_CS', 'utf16', 4);
charsets[2826] = new Collation(2826, 'UCA1400_ICELANDIC_AS_CI', 'utf16', 4);
charsets[2827] = new Collation(2827, 'UCA1400_ICELANDIC_AS_CS', 'utf16', 4);
charsets[2828] = new Collation(2828, 'UCA1400_ICELANDIC_NOPAD_AI_CI', 'utf16', 4);
charsets[2829] = new Collation(2829, 'UCA1400_ICELANDIC_NOPAD_AI_CS', 'utf16', 4);
charsets[2830] = new Collation(2830, 'UCA1400_ICELANDIC_NOPAD_AS_CI', 'utf16', 4);
charsets[2831] = new Collation(2831, 'UCA1400_ICELANDIC_NOPAD_AS_CS', 'utf16', 4);
charsets[2832] = new Collation(2832, 'UCA1400_LATVIAN_AI_CI', 'utf16', 4);
charsets[2833] = new Collation(2833, 'UCA1400_LATVIAN_AI_CS', 'utf16', 4);
charsets[2834] = new Collation(2834, 'UCA1400_LATVIAN_AS_CI', 'utf16', 4);
charsets[2835] = new Collation(2835, 'UCA1400_LATVIAN_AS_CS', 'utf16', 4);
charsets[2836] = new Collation(2836, 'UCA1400_LATVIAN_NOPAD_AI_CI', 'utf16', 4);
charsets[2837] = new Collation(2837, 'UCA1400_LATVIAN_NOPAD_AI_CS', 'utf16', 4);
charsets[2838] = new Collation(2838, 'UCA1400_LATVIAN_NOPAD_AS_CI', 'utf16', 4);
charsets[2839] = new Collation(2839, 'UCA1400_LATVIAN_NOPAD_AS_CS', 'utf16', 4);
charsets[2840] = new Collation(2840, 'UCA1400_ROMANIAN_AI_CI', 'utf16', 4);
charsets[2841] = new Collation(2841, 'UCA1400_ROMANIAN_AI_CS', 'utf16', 4);
charsets[2842] = new Collation(2842, 'UCA1400_ROMANIAN_AS_CI', 'utf16', 4);
charsets[2843] = new Collation(2843, 'UCA1400_ROMANIAN_AS_CS', 'utf16', 4);
charsets[2844] = new Collation(2844, 'UCA1400_ROMANIAN_NOPAD_AI_CI', 'utf16', 4);
charsets[2845] = new Collation(2845, 'UCA1400_ROMANIAN_NOPAD_AI_CS', 'utf16', 4);
charsets[2846] = new Collation(2846, 'UCA1400_ROMANIAN_NOPAD_AS_CI', 'utf16', 4);
charsets[2847] = new Collation(2847, 'UCA1400_ROMANIAN_NOPAD_AS_CS', 'utf16', 4);
charsets[2848] = new Collation(2848, 'UCA1400_SLOVENIAN_AI_CI', 'utf16', 4);
charsets[2849] = new Collation(2849, 'UCA1400_SLOVENIAN_AI_CS', 'utf16', 4);
charsets[2850] = new Collation(2850, 'UCA1400_SLOVENIAN_AS_CI', 'utf16', 4);
charsets[2851] = new Collation(2851, 'UCA1400_SLOVENIAN_AS_CS', 'utf16', 4);
charsets[2852] = new Collation(2852, 'UCA1400_SLOVENIAN_NOPAD_AI_CI', 'utf16', 4);
charsets[2853] = new Collation(2853, 'UCA1400_SLOVENIAN_NOPAD_AI_CS', 'utf16', 4);
charsets[2854] = new Collation(2854, 'UCA1400_SLOVENIAN_NOPAD_AS_CI', 'utf16', 4);
charsets[2855] = new Collation(2855, 'UCA1400_SLOVENIAN_NOPAD_AS_CS', 'utf16', 4);
charsets[2856] = new Collation(2856, 'UCA1400_POLISH_AI_CI', 'utf16', 4);
charsets[2857] = new Collation(2857, 'UCA1400_POLISH_AI_CS', 'utf16', 4);
charsets[2858] = new Collation(2858, 'UCA1400_POLISH_AS_CI', 'utf16', 4);
charsets[2859] = new Collation(2859, 'UCA1400_POLISH_AS_CS', 'utf16', 4);
charsets[2860] = new Collation(2860, 'UCA1400_POLISH_NOPAD_AI_CI', 'utf16', 4);
charsets[2861] = new Collation(2861, 'UCA1400_POLISH_NOPAD_AI_CS', 'utf16', 4);
charsets[2862] = new Collation(2862, 'UCA1400_POLISH_NOPAD_AS_CI', 'utf16', 4);
charsets[2863] = new Collation(2863, 'UCA1400_POLISH_NOPAD_AS_CS', 'utf16', 4);
charsets[2864] = new Collation(2864, 'UCA1400_ESTONIAN_AI_CI', 'utf16', 4);
charsets[2865] = new Collation(2865, 'UCA1400_ESTONIAN_AI_CS', 'utf16', 4);
charsets[2866] = new Collation(2866, 'UCA1400_ESTONIAN_AS_CI', 'utf16', 4);
charsets[2867] = new Collation(2867, 'UCA1400_ESTONIAN_AS_CS', 'utf16', 4);
charsets[2868] = new Collation(2868, 'UCA1400_ESTONIAN_NOPAD_AI_CI', 'utf16', 4);
charsets[2869] = new Collation(2869, 'UCA1400_ESTONIAN_NOPAD_AI_CS', 'utf16', 4);
charsets[2870] = new Collation(2870, 'UCA1400_ESTONIAN_NOPAD_AS_CI', 'utf16', 4);
charsets[2871] = new Collation(2871, 'UCA1400_ESTONIAN_NOPAD_AS_CS', 'utf16', 4);
charsets[2872] = new Collation(2872, 'UCA1400_SPANISH_AI_CI', 'utf16', 4);
charsets[2873] = new Collation(2873, 'UCA1400_SPANISH_AI_CS', 'utf16', 4);
charsets[2874] = new Collation(2874, 'UCA1400_SPANISH_AS_CI', 'utf16', 4);
charsets[2875] = new Collation(2875, 'UCA1400_SPANISH_AS_CS', 'utf16', 4);
charsets[2876] = new Collation(2876, 'UCA1400_SPANISH_NOPAD_AI_CI', 'utf16', 4);
charsets[2877] = new Collation(2877, 'UCA1400_SPANISH_NOPAD_AI_CS', 'utf16', 4);
charsets[2878] = new Collation(2878, 'UCA1400_SPANISH_NOPAD_AS_CI', 'utf16', 4);
charsets[2879] = new Collation(2879, 'UCA1400_SPANISH_NOPAD_AS_CS', 'utf16', 4);
charsets[2880] = new Collation(2880, 'UCA1400_SWEDISH_AI_CI', 'utf16', 4);
charsets[2881] = new Collation(2881, 'UCA1400_SWEDISH_AI_CS', 'utf16', 4);
charsets[2882] = new Collation(2882, 'UCA1400_SWEDISH_AS_CI', 'utf16', 4);
charsets[2883] = new Collation(2883, 'UCA1400_SWEDISH_AS_CS', 'utf16', 4);
charsets[2884] = new Collation(2884, 'UCA1400_SWEDISH_NOPAD_AI_CI', 'utf16', 4);
charsets[2885] = new Collation(2885, 'UCA1400_SWEDISH_NOPAD_AI_CS', 'utf16', 4);
charsets[2886] = new Collation(2886, 'UCA1400_SWEDISH_NOPAD_AS_CI', 'utf16', 4);
charsets[2887] = new Collation(2887, 'UCA1400_SWEDISH_NOPAD_AS_CS', 'utf16', 4);
charsets[2888] = new Collation(2888, 'UCA1400_TURKISH_AI_CI', 'utf16', 4);
charsets[2889] = new Collation(2889, 'UCA1400_TURKISH_AI_CS', 'utf16', 4);
charsets[2890] = new Collation(2890, 'UCA1400_TURKISH_AS_CI', 'utf16', 4);
charsets[2891] = new Collation(2891, 'UCA1400_TURKISH_AS_CS', 'utf16', 4);
charsets[2892] = new Collation(2892, 'UCA1400_TURKISH_NOPAD_AI_CI', 'utf16', 4);
charsets[2893] = new Collation(2893, 'UCA1400_TURKISH_NOPAD_AI_CS', 'utf16', 4);
charsets[2894] = new Collation(2894, 'UCA1400_TURKISH_NOPAD_AS_CI', 'utf16', 4);
charsets[2895] = new Collation(2895, 'UCA1400_TURKISH_NOPAD_AS_CS', 'utf16', 4);
charsets[2896] = new Collation(2896, 'UCA1400_CZECH_AI_CI', 'utf16', 4);
charsets[2897] = new Collation(2897, 'UCA1400_CZECH_AI_CS', 'utf16', 4);
charsets[2898] = new Collation(2898, 'UCA1400_CZECH_AS_CI', 'utf16', 4);
charsets[2899] = new Collation(2899, 'UCA1400_CZECH_AS_CS', 'utf16', 4);
charsets[2900] = new Collation(2900, 'UCA1400_CZECH_NOPAD_AI_CI', 'utf16', 4);
charsets[2901] = new Collation(2901, 'UCA1400_CZECH_NOPAD_AI_CS', 'utf16', 4);
charsets[2902] = new Collation(2902, 'UCA1400_CZECH_NOPAD_AS_CI', 'utf16', 4);
charsets[2903] = new Collation(2903, 'UCA1400_CZECH_NOPAD_AS_CS', 'utf16', 4);
charsets[2904] = new Collation(2904, 'UCA1400_DANISH_AI_CI', 'utf16', 4);
charsets[2905] = new Collation(2905, 'UCA1400_DANISH_AI_CS', 'utf16', 4);
charsets[2906] = new Collation(2906, 'UCA1400_DANISH_AS_CI', 'utf16', 4);
charsets[2907] = new Collation(2907, 'UCA1400_DANISH_AS_CS', 'utf16', 4);
charsets[2908] = new Collation(2908, 'UCA1400_DANISH_NOPAD_AI_CI', 'utf16', 4);
charsets[2909] = new Collation(2909, 'UCA1400_DANISH_NOPAD_AI_CS', 'utf16', 4);
charsets[2910] = new Collation(2910, 'UCA1400_DANISH_NOPAD_AS_CI', 'utf16', 4);
charsets[2911] = new Collation(2911, 'UCA1400_DANISH_NOPAD_AS_CS', 'utf16', 4);
charsets[2912] = new Collation(2912, 'UCA1400_LITHUANIAN_AI_CI', 'utf16', 4);
charsets[2913] = new Collation(2913, 'UCA1400_LITHUANIAN_AI_CS', 'utf16', 4);
charsets[2914] = new Collation(2914, 'UCA1400_LITHUANIAN_AS_CI', 'utf16', 4);
charsets[2915] = new Collation(2915, 'UCA1400_LITHUANIAN_AS_CS', 'utf16', 4);
charsets[2916] = new Collation(2916, 'UCA1400_LITHUANIAN_NOPAD_AI_CI', 'utf16', 4);
charsets[2917] = new Collation(2917, 'UCA1400_LITHUANIAN_NOPAD_AI_CS', 'utf16', 4);
charsets[2918] = new Collation(2918, 'UCA1400_LITHUANIAN_NOPAD_AS_CI', 'utf16', 4);
charsets[2919] = new Collation(2919, 'UCA1400_LITHUANIAN_NOPAD_AS_CS', 'utf16', 4);
charsets[2920] = new Collation(2920, 'UCA1400_SLOVAK_AI_CI', 'utf16', 4);
charsets[2921] = new Collation(2921, 'UCA1400_SLOVAK_AI_CS', 'utf16', 4);
charsets[2922] = new Collation(2922, 'UCA1400_SLOVAK_AS_CI', 'utf16', 4);
charsets[2923] = new Collation(2923, 'UCA1400_SLOVAK_AS_CS', 'utf16', 4);
charsets[2924] = new Collation(2924, 'UCA1400_SLOVAK_NOPAD_AI_CI', 'utf16', 4);
charsets[2925] = new Collation(2925, 'UCA1400_SLOVAK_NOPAD_AI_CS', 'utf16', 4);
charsets[2926] = new Collation(2926, 'UCA1400_SLOVAK_NOPAD_AS_CI', 'utf16', 4);
charsets[2927] = new Collation(2927, 'UCA1400_SLOVAK_NOPAD_AS_CS', 'utf16', 4);
charsets[2928] = new Collation(2928, 'UCA1400_SPANISH2_AI_CI', 'utf16', 4);
charsets[2929] = new Collation(2929, 'UCA1400_SPANISH2_AI_CS', 'utf16', 4);
charsets[2930] = new Collation(2930, 'UCA1400_SPANISH2_AS_CI', 'utf16', 4);
charsets[2931] = new Collation(2931, 'UCA1400_SPANISH2_AS_CS', 'utf16', 4);
charsets[2932] = new Collation(2932, 'UCA1400_SPANISH2_NOPAD_AI_CI', 'utf16', 4);
charsets[2933] = new Collation(2933, 'UCA1400_SPANISH2_NOPAD_AI_CS', 'utf16', 4);
charsets[2934] = new Collation(2934, 'UCA1400_SPANISH2_NOPAD_AS_CI', 'utf16', 4);
charsets[2935] = new Collation(2935, 'UCA1400_SPANISH2_NOPAD_AS_CS', 'utf16', 4);
charsets[2936] = new Collation(2936, 'UCA1400_ROMAN_AI_CI', 'utf16', 4);
charsets[2937] = new Collation(2937, 'UCA1400_ROMAN_AI_CS', 'utf16', 4);
charsets[2938] = new Collation(2938, 'UCA1400_ROMAN_AS_CI', 'utf16', 4);
charsets[2939] = new Collation(2939, 'UCA1400_ROMAN_AS_CS', 'utf16', 4);
charsets[2940] = new Collation(2940, 'UCA1400_ROMAN_NOPAD_AI_CI', 'utf16', 4);
charsets[2941] = new Collation(2941, 'UCA1400_ROMAN_NOPAD_AI_CS', 'utf16', 4);
charsets[2942] = new Collation(2942, 'UCA1400_ROMAN_NOPAD_AS_CI', 'utf16', 4);
charsets[2943] = new Collation(2943, 'UCA1400_ROMAN_NOPAD_AS_CS', 'utf16', 4);
charsets[2944] = new Collation(2944, 'UCA1400_PERSIAN_AI_CI', 'utf16', 4);
charsets[2945] = new Collation(2945, 'UCA1400_PERSIAN_AI_CS', 'utf16', 4);
charsets[2946] = new Collation(2946, 'UCA1400_PERSIAN_AS_CI', 'utf16', 4);
charsets[2947] = new Collation(2947, 'UCA1400_PERSIAN_AS_CS', 'utf16', 4);
charsets[2948] = new Collation(2948, 'UCA1400_PERSIAN_NOPAD_AI_CI', 'utf16', 4);
charsets[2949] = new Collation(2949, 'UCA1400_PERSIAN_NOPAD_AI_CS', 'utf16', 4);
charsets[2950] = new Collation(2950, 'UCA1400_PERSIAN_NOPAD_AS_CI', 'utf16', 4);
charsets[2951] = new Collation(2951, 'UCA1400_PERSIAN_NOPAD_AS_CS', 'utf16', 4);
charsets[2952] = new Collation(2952, 'UCA1400_ESPERANTO_AI_CI', 'utf16', 4);
charsets[2953] = new Collation(2953, 'UCA1400_ESPERANTO_AI_CS', 'utf16', 4);
charsets[2954] = new Collation(2954, 'UCA1400_ESPERANTO_AS_CI', 'utf16', 4);
charsets[2955] = new Collation(2955, 'UCA1400_ESPERANTO_AS_CS', 'utf16', 4);
charsets[2956] = new Collation(2956, 'UCA1400_ESPERANTO_NOPAD_AI_CI', 'utf16', 4);
charsets[2957] = new Collation(2957, 'UCA1400_ESPERANTO_NOPAD_AI_CS', 'utf16', 4);
charsets[2958] = new Collation(2958, 'UCA1400_ESPERANTO_NOPAD_AS_CI', 'utf16', 4);
charsets[2959] = new Collation(2959, 'UCA1400_ESPERANTO_NOPAD_AS_CS', 'utf16', 4);
charsets[2960] = new Collation(2960, 'UCA1400_HUNGARIAN_AI_CI', 'utf16', 4);
charsets[2961] = new Collation(2961, 'UCA1400_HUNGARIAN_AI_CS', 'utf16', 4);
charsets[2962] = new Collation(2962, 'UCA1400_HUNGARIAN_AS_CI', 'utf16', 4);
charsets[2963] = new Collation(2963, 'UCA1400_HUNGARIAN_AS_CS', 'utf16', 4);
charsets[2964] = new Collation(2964, 'UCA1400_HUNGARIAN_NOPAD_AI_CI', 'utf16', 4);
charsets[2965] = new Collation(2965, 'UCA1400_HUNGARIAN_NOPAD_AI_CS', 'utf16', 4);
charsets[2966] = new Collation(2966, 'UCA1400_HUNGARIAN_NOPAD_AS_CI', 'utf16', 4);
charsets[2967] = new Collation(2967, 'UCA1400_HUNGARIAN_NOPAD_AS_CS', 'utf16', 4);
charsets[2968] = new Collation(2968, 'UCA1400_SINHALA_AI_CI', 'utf16', 4);
charsets[2969] = new Collation(2969, 'UCA1400_SINHALA_AI_CS', 'utf16', 4);
charsets[2970] = new Collation(2970, 'UCA1400_SINHALA_AS_CI', 'utf16', 4);
charsets[2971] = new Collation(2971, 'UCA1400_SINHALA_AS_CS', 'utf16', 4);
charsets[2972] = new Collation(2972, 'UCA1400_SINHALA_NOPAD_AI_CI', 'utf16', 4);
charsets[2973] = new Collation(2973, 'UCA1400_SINHALA_NOPAD_AI_CS', 'utf16', 4);
charsets[2974] = new Collation(2974, 'UCA1400_SINHALA_NOPAD_AS_CI', 'utf16', 4);
charsets[2975] = new Collation(2975, 'UCA1400_SINHALA_NOPAD_AS_CS', 'utf16', 4);
charsets[2976] = new Collation(2976, 'UCA1400_GERMAN2_AI_CI', 'utf16', 4);
charsets[2977] = new Collation(2977, 'UCA1400_GERMAN2_AI_CS', 'utf16', 4);
charsets[2978] = new Collation(2978, 'UCA1400_GERMAN2_AS_CI', 'utf16', 4);
charsets[2979] = new Collation(2979, 'UCA1400_GERMAN2_AS_CS', 'utf16', 4);
charsets[2980] = new Collation(2980, 'UCA1400_GERMAN2_NOPAD_AI_CI', 'utf16', 4);
charsets[2981] = new Collation(2981, 'UCA1400_GERMAN2_NOPAD_AI_CS', 'utf16', 4);
charsets[2982] = new Collation(2982, 'UCA1400_GERMAN2_NOPAD_AS_CI', 'utf16', 4);
charsets[2983] = new Collation(2983, 'UCA1400_GERMAN2_NOPAD_AS_CS', 'utf16', 4);
charsets[3000] = new Collation(3000, 'UCA1400_VIETNAMESE_AI_CI', 'utf16', 4);
charsets[3001] = new Collation(3001, 'UCA1400_VIETNAMESE_AI_CS', 'utf16', 4);
charsets[3002] = new Collation(3002, 'UCA1400_VIETNAMESE_AS_CI', 'utf16', 4);
charsets[3003] = new Collation(3003, 'UCA1400_VIETNAMESE_AS_CS', 'utf16', 4);
charsets[3004] = new Collation(3004, 'UCA1400_VIETNAMESE_NOPAD_AI_CI', 'utf16', 4);
charsets[3005] = new Collation(3005, 'UCA1400_VIETNAMESE_NOPAD_AI_CS', 'utf16', 4);
charsets[3006] = new Collation(3006, 'UCA1400_VIETNAMESE_NOPAD_AS_CI', 'utf16', 4);
charsets[3007] = new Collation(3007, 'UCA1400_VIETNAMESE_NOPAD_AS_CS', 'utf16', 4);
charsets[3008] = new Collation(3008, 'UCA1400_CROATIAN_AI_CI', 'utf16', 4);
charsets[3009] = new Collation(3009, 'UCA1400_CROATIAN_AI_CS', 'utf16', 4);
charsets[3010] = new Collation(3010, 'UCA1400_CROATIAN_AS_CI', 'utf16', 4);
charsets[3011] = new Collation(3011, 'UCA1400_CROATIAN_AS_CS', 'utf16', 4);
charsets[3012] = new Collation(3012, 'UCA1400_CROATIAN_NOPAD_AI_CI', 'utf16', 4);
charsets[3013] = new Collation(3013, 'UCA1400_CROATIAN_NOPAD_AI_CS', 'utf16', 4);
charsets[3014] = new Collation(3014, 'UCA1400_CROATIAN_NOPAD_AS_CI', 'utf16', 4);
charsets[3015] = new Collation(3015, 'UCA1400_CROATIAN_NOPAD_AS_CS', 'utf16', 4);
charsets[3072] = new Collation(3072, 'UCA1400_AI_CI', 'utf32', 4);
charsets[3073] = new Collation(3073, 'UCA1400_AI_CS', 'utf32', 4);
charsets[3074] = new Collation(3074, 'UCA1400_AS_CI', 'utf32', 4);
charsets[3075] = new Collation(3075, 'UCA1400_AS_CS', 'utf32', 4);
charsets[3076] = new Collation(3076, 'UCA1400_NOPAD_AI_CI', 'utf32', 4);
charsets[3077] = new Collation(3077, 'UCA1400_NOPAD_AI_CS', 'utf32', 4);
charsets[3078] = new Collation(3078, 'UCA1400_NOPAD_AS_CI', 'utf32', 4);
charsets[3079] = new Collation(3079, 'UCA1400_NOPAD_AS_CS', 'utf32', 4);
charsets[3080] = new Collation(3080, 'UCA1400_ICELANDIC_AI_CI', 'utf32', 4);
charsets[3081] = new Collation(3081, 'UCA1400_ICELANDIC_AI_CS', 'utf32', 4);
charsets[3082] = new Collation(3082, 'UCA1400_ICELANDIC_AS_CI', 'utf32', 4);
charsets[3083] = new Collation(3083, 'UCA1400_ICELANDIC_AS_CS', 'utf32', 4);
charsets[3084] = new Collation(3084, 'UCA1400_ICELANDIC_NOPAD_AI_CI', 'utf32', 4);
charsets[3085] = new Collation(3085, 'UCA1400_ICELANDIC_NOPAD_AI_CS', 'utf32', 4);
charsets[3086] = new Collation(3086, 'UCA1400_ICELANDIC_NOPAD_AS_CI', 'utf32', 4);
charsets[3087] = new Collation(3087, 'UCA1400_ICELANDIC_NOPAD_AS_CS', 'utf32', 4);
charsets[3088] = new Collation(3088, 'UCA1400_LATVIAN_AI_CI', 'utf32', 4);
charsets[3089] = new Collation(3089, 'UCA1400_LATVIAN_AI_CS', 'utf32', 4);
charsets[3090] = new Collation(3090, 'UCA1400_LATVIAN_AS_CI', 'utf32', 4);
charsets[3091] = new Collation(3091, 'UCA1400_LATVIAN_AS_CS', 'utf32', 4);
charsets[3092] = new Collation(3092, 'UCA1400_LATVIAN_NOPAD_AI_CI', 'utf32', 4);
charsets[3093] = new Collation(3093, 'UCA1400_LATVIAN_NOPAD_AI_CS', 'utf32', 4);
charsets[3094] = new Collation(3094, 'UCA1400_LATVIAN_NOPAD_AS_CI', 'utf32', 4);
charsets[3095] = new Collation(3095, 'UCA1400_LATVIAN_NOPAD_AS_CS', 'utf32', 4);
charsets[3096] = new Collation(3096, 'UCA1400_ROMANIAN_AI_CI', 'utf32', 4);
charsets[3097] = new Collation(3097, 'UCA1400_ROMANIAN_AI_CS', 'utf32', 4);
charsets[3098] = new Collation(3098, 'UCA1400_ROMANIAN_AS_CI', 'utf32', 4);
charsets[3099] = new Collation(3099, 'UCA1400_ROMANIAN_AS_CS', 'utf32', 4);
charsets[3100] = new Collation(3100, 'UCA1400_ROMANIAN_NOPAD_AI_CI', 'utf32', 4);
charsets[3101] = new Collation(3101, 'UCA1400_ROMANIAN_NOPAD_AI_CS', 'utf32', 4);
charsets[3102] = new Collation(3102, 'UCA1400_ROMANIAN_NOPAD_AS_CI', 'utf32', 4);
charsets[3103] = new Collation(3103, 'UCA1400_ROMANIAN_NOPAD_AS_CS', 'utf32', 4);
charsets[3104] = new Collation(3104, 'UCA1400_SLOVENIAN_AI_CI', 'utf32', 4);
charsets[3105] = new Collation(3105, 'UCA1400_SLOVENIAN_AI_CS', 'utf32', 4);
charsets[3106] = new Collation(3106, 'UCA1400_SLOVENIAN_AS_CI', 'utf32', 4);
charsets[3107] = new Collation(3107, 'UCA1400_SLOVENIAN_AS_CS', 'utf32', 4);
charsets[3108] = new Collation(3108, 'UCA1400_SLOVENIAN_NOPAD_AI_CI', 'utf32', 4);
charsets[3109] = new Collation(3109, 'UCA1400_SLOVENIAN_NOPAD_AI_CS', 'utf32', 4);
charsets[3110] = new Collation(3110, 'UCA1400_SLOVENIAN_NOPAD_AS_CI', 'utf32', 4);
charsets[3111] = new Collation(3111, 'UCA1400_SLOVENIAN_NOPAD_AS_CS', 'utf32', 4);
charsets[3112] = new Collation(3112, 'UCA1400_POLISH_AI_CI', 'utf32', 4);
charsets[3113] = new Collation(3113, 'UCA1400_POLISH_AI_CS', 'utf32', 4);
charsets[3114] = new Collation(3114, 'UCA1400_POLISH_AS_CI', 'utf32', 4);
charsets[3115] = new Collation(3115, 'UCA1400_POLISH_AS_CS', 'utf32', 4);
charsets[3116] = new Collation(3116, 'UCA1400_POLISH_NOPAD_AI_CI', 'utf32', 4);
charsets[3117] = new Collation(3117, 'UCA1400_POLISH_NOPAD_AI_CS', 'utf32', 4);
charsets[3118] = new Collation(3118, 'UCA1400_POLISH_NOPAD_AS_CI', 'utf32', 4);
charsets[3119] = new Collation(3119, 'UCA1400_POLISH_NOPAD_AS_CS', 'utf32', 4);
charsets[3120] = new Collation(3120, 'UCA1400_ESTONIAN_AI_CI', 'utf32', 4);
charsets[3121] = new Collation(3121, 'UCA1400_ESTONIAN_AI_CS', 'utf32', 4);
charsets[3122] = new Collation(3122, 'UCA1400_ESTONIAN_AS_CI', 'utf32', 4);
charsets[3123] = new Collation(3123, 'UCA1400_ESTONIAN_AS_CS', 'utf32', 4);
charsets[3124] = new Collation(3124, 'UCA1400_ESTONIAN_NOPAD_AI_CI', 'utf32', 4);
charsets[3125] = new Collation(3125, 'UCA1400_ESTONIAN_NOPAD_AI_CS', 'utf32', 4);
charsets[3126] = new Collation(3126, 'UCA1400_ESTONIAN_NOPAD_AS_CI', 'utf32', 4);
charsets[3127] = new Collation(3127, 'UCA1400_ESTONIAN_NOPAD_AS_CS', 'utf32', 4);
charsets[3128] = new Collation(3128, 'UCA1400_SPANISH_AI_CI', 'utf32', 4);
charsets[3129] = new Collation(3129, 'UCA1400_SPANISH_AI_CS', 'utf32', 4);
charsets[3130] = new Collation(3130, 'UCA1400_SPANISH_AS_CI', 'utf32', 4);
charsets[3131] = new Collation(3131, 'UCA1400_SPANISH_AS_CS', 'utf32', 4);
charsets[3132] = new Collation(3132, 'UCA1400_SPANISH_NOPAD_AI_CI', 'utf32', 4);
charsets[3133] = new Collation(3133, 'UCA1400_SPANISH_NOPAD_AI_CS', 'utf32', 4);
charsets[3134] = new Collation(3134, 'UCA1400_SPANISH_NOPAD_AS_CI', 'utf32', 4);
charsets[3135] = new Collation(3135, 'UCA1400_SPANISH_NOPAD_AS_CS', 'utf32', 4);
charsets[3136] = new Collation(3136, 'UCA1400_SWEDISH_AI_CI', 'utf32', 4);
charsets[3137] = new Collation(3137, 'UCA1400_SWEDISH_AI_CS', 'utf32', 4);
charsets[3138] = new Collation(3138, 'UCA1400_SWEDISH_AS_CI', 'utf32', 4);
charsets[3139] = new Collation(3139, 'UCA1400_SWEDISH_AS_CS', 'utf32', 4);
charsets[3140] = new Collation(3140, 'UCA1400_SWEDISH_NOPAD_AI_CI', 'utf32', 4);
charsets[3141] = new Collation(3141, 'UCA1400_SWEDISH_NOPAD_AI_CS', 'utf32', 4);
charsets[3142] = new Collation(3142, 'UCA1400_SWEDISH_NOPAD_AS_CI', 'utf32', 4);
charsets[3143] = new Collation(3143, 'UCA1400_SWEDISH_NOPAD_AS_CS', 'utf32', 4);
charsets[3144] = new Collation(3144, 'UCA1400_TURKISH_AI_CI', 'utf32', 4);
charsets[3145] = new Collation(3145, 'UCA1400_TURKISH_AI_CS', 'utf32', 4);
charsets[3146] = new Collation(3146, 'UCA1400_TURKISH_AS_CI', 'utf32', 4);
charsets[3147] = new Collation(3147, 'UCA1400_TURKISH_AS_CS', 'utf32', 4);
charsets[3148] = new Collation(3148, 'UCA1400_TURKISH_NOPAD_AI_CI', 'utf32', 4);
charsets[3149] = new Collation(3149, 'UCA1400_TURKISH_NOPAD_AI_CS', 'utf32', 4);
charsets[3150] = new Collation(3150, 'UCA1400_TURKISH_NOPAD_AS_CI', 'utf32', 4);
charsets[3151] = new Collation(3151, 'UCA1400_TURKISH_NOPAD_AS_CS', 'utf32', 4);
charsets[3152] = new Collation(3152, 'UCA1400_CZECH_AI_CI', 'utf32', 4);
charsets[3153] = new Collation(3153, 'UCA1400_CZECH_AI_CS', 'utf32', 4);
charsets[3154] = new Collation(3154, 'UCA1400_CZECH_AS_CI', 'utf32', 4);
charsets[3155] = new Collation(3155, 'UCA1400_CZECH_AS_CS', 'utf32', 4);
charsets[3156] = new Collation(3156, 'UCA1400_CZECH_NOPAD_AI_CI', 'utf32', 4);
charsets[3157] = new Collation(3157, 'UCA1400_CZECH_NOPAD_AI_CS', 'utf32', 4);
charsets[3158] = new Collation(3158, 'UCA1400_CZECH_NOPAD_AS_CI', 'utf32', 4);
charsets[3159] = new Collation(3159, 'UCA1400_CZECH_NOPAD_AS_CS', 'utf32', 4);
charsets[3160] = new Collation(3160, 'UCA1400_DANISH_AI_CI', 'utf32', 4);
charsets[3161] = new Collation(3161, 'UCA1400_DANISH_AI_CS', 'utf32', 4);
charsets[3162] = new Collation(3162, 'UCA1400_DANISH_AS_CI', 'utf32', 4);
charsets[3163] = new Collation(3163, 'UCA1400_DANISH_AS_CS', 'utf32', 4);
charsets[3164] = new Collation(3164, 'UCA1400_DANISH_NOPAD_AI_CI', 'utf32', 4);
charsets[3165] = new Collation(3165, 'UCA1400_DANISH_NOPAD_AI_CS', 'utf32', 4);
charsets[3166] = new Collation(3166, 'UCA1400_DANISH_NOPAD_AS_CI', 'utf32', 4);
charsets[3167] = new Collation(3167, 'UCA1400_DANISH_NOPAD_AS_CS', 'utf32', 4);
charsets[3168] = new Collation(3168, 'UCA1400_LITHUANIAN_AI_CI', 'utf32', 4);
charsets[3169] = new Collation(3169, 'UCA1400_LITHUANIAN_AI_CS', 'utf32', 4);
charsets[3170] = new Collation(3170, 'UCA1400_LITHUANIAN_AS_CI', 'utf32', 4);
charsets[3171] = new Collation(3171, 'UCA1400_LITHUANIAN_AS_CS', 'utf32', 4);
charsets[3172] = new Collation(3172, 'UCA1400_LITHUANIAN_NOPAD_AI_CI', 'utf32', 4);
charsets[3173] = new Collation(3173, 'UCA1400_LITHUANIAN_NOPAD_AI_CS', 'utf32', 4);
charsets[3174] = new Collation(3174, 'UCA1400_LITHUANIAN_NOPAD_AS_CI', 'utf32', 4);
charsets[3175] = new Collation(3175, 'UCA1400_LITHUANIAN_NOPAD_AS_CS', 'utf32', 4);
charsets[3176] = new Collation(3176, 'UCA1400_SLOVAK_AI_CI', 'utf32', 4);
charsets[3177] = new Collation(3177, 'UCA1400_SLOVAK_AI_CS', 'utf32', 4);
charsets[3178] = new Collation(3178, 'UCA1400_SLOVAK_AS_CI', 'utf32', 4);
charsets[3179] = new Collation(3179, 'UCA1400_SLOVAK_AS_CS', 'utf32', 4);
charsets[3180] = new Collation(3180, 'UCA1400_SLOVAK_NOPAD_AI_CI', 'utf32', 4);
charsets[3181] = new Collation(3181, 'UCA1400_SLOVAK_NOPAD_AI_CS', 'utf32', 4);
charsets[3182] = new Collation(3182, 'UCA1400_SLOVAK_NOPAD_AS_CI', 'utf32', 4);
charsets[3183] = new Collation(3183, 'UCA1400_SLOVAK_NOPAD_AS_CS', 'utf32', 4);
charsets[3184] = new Collation(3184, 'UCA1400_SPANISH2_AI_CI', 'utf32', 4);
charsets[3185] = new Collation(3185, 'UCA1400_SPANISH2_AI_CS', 'utf32', 4);
charsets[3186] = new Collation(3186, 'UCA1400_SPANISH2_AS_CI', 'utf32', 4);
charsets[3187] = new Collation(3187, 'UCA1400_SPANISH2_AS_CS', 'utf32', 4);
charsets[3188] = new Collation(3188, 'UCA1400_SPANISH2_NOPAD_AI_CI', 'utf32', 4);
charsets[3189] = new Collation(3189, 'UCA1400_SPANISH2_NOPAD_AI_CS', 'utf32', 4);
charsets[3190] = new Collation(3190, 'UCA1400_SPANISH2_NOPAD_AS_CI', 'utf32', 4);
charsets[3191] = new Collation(3191, 'UCA1400_SPANISH2_NOPAD_AS_CS', 'utf32', 4);
charsets[3192] = new Collation(3192, 'UCA1400_ROMAN_AI_CI', 'utf32', 4);
charsets[3193] = new Collation(3193, 'UCA1400_ROMAN_AI_CS', 'utf32', 4);
charsets[3194] = new Collation(3194, 'UCA1400_ROMAN_AS_CI', 'utf32', 4);
charsets[3195] = new Collation(3195, 'UCA1400_ROMAN_AS_CS', 'utf32', 4);
charsets[3196] = new Collation(3196, 'UCA1400_ROMAN_NOPAD_AI_CI', 'utf32', 4);
charsets[3197] = new Collation(3197, 'UCA1400_ROMAN_NOPAD_AI_CS', 'utf32', 4);
charsets[3198] = new Collation(3198, 'UCA1400_ROMAN_NOPAD_AS_CI', 'utf32', 4);
charsets[3199] = new Collation(3199, 'UCA1400_ROMAN_NOPAD_AS_CS', 'utf32', 4);
charsets[3200] = new Collation(3200, 'UCA1400_PERSIAN_AI_CI', 'utf32', 4);
charsets[3201] = new Collation(3201, 'UCA1400_PERSIAN_AI_CS', 'utf32', 4);
charsets[3202] = new Collation(3202, 'UCA1400_PERSIAN_AS_CI', 'utf32', 4);
charsets[3203] = new Collation(3203, 'UCA1400_PERSIAN_AS_CS', 'utf32', 4);
charsets[3204] = new Collation(3204, 'UCA1400_PERSIAN_NOPAD_AI_CI', 'utf32', 4);
charsets[3205] = new Collation(3205, 'UCA1400_PERSIAN_NOPAD_AI_CS', 'utf32', 4);
charsets[3206] = new Collation(3206, 'UCA1400_PERSIAN_NOPAD_AS_CI', 'utf32', 4);
charsets[3207] = new Collation(3207, 'UCA1400_PERSIAN_NOPAD_AS_CS', 'utf32', 4);
charsets[3208] = new Collation(3208, 'UCA1400_ESPERANTO_AI_CI', 'utf32', 4);
charsets[3209] = new Collation(3209, 'UCA1400_ESPERANTO_AI_CS', 'utf32', 4);
charsets[3210] = new Collation(3210, 'UCA1400_ESPERANTO_AS_CI', 'utf32', 4);
charsets[3211] = new Collation(3211, 'UCA1400_ESPERANTO_AS_CS', 'utf32', 4);
charsets[3212] = new Collation(3212, 'UCA1400_ESPERANTO_NOPAD_AI_CI', 'utf32', 4);
charsets[3213] = new Collation(3213, 'UCA1400_ESPERANTO_NOPAD_AI_CS', 'utf32', 4);
charsets[3214] = new Collation(3214, 'UCA1400_ESPERANTO_NOPAD_AS_CI', 'utf32', 4);
charsets[3215] = new Collation(3215, 'UCA1400_ESPERANTO_NOPAD_AS_CS', 'utf32', 4);
charsets[3216] = new Collation(3216, 'UCA1400_HUNGARIAN_AI_CI', 'utf32', 4);
charsets[3217] = new Collation(3217, 'UCA1400_HUNGARIAN_AI_CS', 'utf32', 4);
charsets[3218] = new Collation(3218, 'UCA1400_HUNGARIAN_AS_CI', 'utf32', 4);
charsets[3219] = new Collation(3219, 'UCA1400_HUNGARIAN_AS_CS', 'utf32', 4);
charsets[3220] = new Collation(3220, 'UCA1400_HUNGARIAN_NOPAD_AI_CI', 'utf32', 4);
charsets[3221] = new Collation(3221, 'UCA1400_HUNGARIAN_NOPAD_AI_CS', 'utf32', 4);
charsets[3222] = new Collation(3222, 'UCA1400_HUNGARIAN_NOPAD_AS_CI', 'utf32', 4);
charsets[3223] = new Collation(3223, 'UCA1400_HUNGARIAN_NOPAD_AS_CS', 'utf32', 4);
charsets[3224] = new Collation(3224, 'UCA1400_SINHALA_AI_CI', 'utf32', 4);
charsets[3225] = new Collation(3225, 'UCA1400_SINHALA_AI_CS', 'utf32', 4);
charsets[3226] = new Collation(3226, 'UCA1400_SINHALA_AS_CI', 'utf32', 4);
charsets[3227] = new Collation(3227, 'UCA1400_SINHALA_AS_CS', 'utf32', 4);
charsets[3228] = new Collation(3228, 'UCA1400_SINHALA_NOPAD_AI_CI', 'utf32', 4);
charsets[3229] = new Collation(3229, 'UCA1400_SINHALA_NOPAD_AI_CS', 'utf32', 4);
charsets[3230] = new Collation(3230, 'UCA1400_SINHALA_NOPAD_AS_CI', 'utf32', 4);
charsets[3231] = new Collation(3231, 'UCA1400_SINHALA_NOPAD_AS_CS', 'utf32', 4);
charsets[3232] = new Collation(3232, 'UCA1400_GERMAN2_AI_CI', 'utf32', 4);
charsets[3233] = new Collation(3233, 'UCA1400_GERMAN2_AI_CS', 'utf32', 4);
charsets[3234] = new Collation(3234, 'UCA1400_GERMAN2_AS_CI', 'utf32', 4);
charsets[3235] = new Collation(3235, 'UCA1400_GERMAN2_AS_CS', 'utf32', 4);
charsets[3236] = new Collation(3236, 'UCA1400_GERMAN2_NOPAD_AI_CI', 'utf32', 4);
charsets[3237] = new Collation(3237, 'UCA1400_GERMAN2_NOPAD_AI_CS', 'utf32', 4);
charsets[3238] = new Collation(3238, 'UCA1400_GERMAN2_NOPAD_AS_CI', 'utf32', 4);
charsets[3239] = new Collation(3239, 'UCA1400_GERMAN2_NOPAD_AS_CS', 'utf32', 4);
charsets[3256] = new Collation(3256, 'UCA1400_VIETNAMESE_AI_CI', 'utf32', 4);
charsets[3257] = new Collation(3257, 'UCA1400_VIETNAMESE_AI_CS', 'utf32', 4);
charsets[3258] = new Collation(3258, 'UCA1400_VIETNAMESE_AS_CI', 'utf32', 4);
charsets[3259] = new Collation(3259, 'UCA1400_VIETNAMESE_AS_CS', 'utf32', 4);
charsets[3260] = new Collation(3260, 'UCA1400_VIETNAMESE_NOPAD_AI_CI', 'utf32', 4);
charsets[3261] = new Collation(3261, 'UCA1400_VIETNAMESE_NOPAD_AI_CS', 'utf32', 4);
charsets[3262] = new Collation(3262, 'UCA1400_VIETNAMESE_NOPAD_AS_CI', 'utf32', 4);
charsets[3263] = new Collation(3263, 'UCA1400_VIETNAMESE_NOPAD_AS_CS', 'utf32', 4);
charsets[3264] = new Collation(3264, 'UCA1400_CROATIAN_AI_CI', 'utf32', 4);
charsets[3265] = new Collation(3265, 'UCA1400_CROATIAN_AI_CS', 'utf32', 4);
charsets[3266] = new Collation(3266, 'UCA1400_CROATIAN_AS_CI', 'utf32', 4);
charsets[3267] = new Collation(3267, 'UCA1400_CROATIAN_AS_CS', 'utf32', 4);
charsets[3268] = new Collation(3268, 'UCA1400_CROATIAN_NOPAD_AI_CI', 'utf32', 4);
charsets[3269] = new Collation(3269, 'UCA1400_CROATIAN_NOPAD_AI_CS', 'utf32', 4);
charsets[3270] = new Collation(3270, 'UCA1400_CROATIAN_NOPAD_AS_CI', 'utf32', 4);
charsets[3271] = new Collation(3271, 'UCA1400_CROATIAN_NOPAD_AS_CS', 'utf32', 4);

for (let i = 0; i < charsets.length; i++) {
  let collation = charsets[i];
  if (collation) {
    Collation.prototype[collation.name] = collation;
  }
}

/**
 * Map charset to default collation
 *
 * created with query:
 *  SELECT CONCAT(' defaultCharsets[\'',  co.character_set_name , '\'] = charsets[', CAST(co.ID as char), '];')
 *  FROM information_schema.COLLATIONS co WHERE co.IS_DEFAULT = 'Yes' ORDER BY co.ID ASC;
 */
defaultCharsets['big5'] = charsets[1];
defaultCharsets['dec8'] = charsets[3];
defaultCharsets['cp850'] = charsets[4];
defaultCharsets['hp8'] = charsets[6];
defaultCharsets['koi8r'] = charsets[7];
defaultCharsets['latin1'] = charsets[8];
defaultCharsets['latin2'] = charsets[9];
defaultCharsets['swe7'] = charsets[10];
defaultCharsets['ascii'] = charsets[11];
defaultCharsets['ujis'] = charsets[12];
defaultCharsets['sjis'] = charsets[13];
defaultCharsets['hebrew'] = charsets[16];
defaultCharsets['tis620'] = charsets[18];
defaultCharsets['euckr'] = charsets[19];
defaultCharsets['koi8u'] = charsets[22];
defaultCharsets['gb2312'] = charsets[24];
defaultCharsets['greek'] = charsets[25];
defaultCharsets['cp1250'] = charsets[26];
defaultCharsets['gbk'] = charsets[28];
defaultCharsets['latin5'] = charsets[30];
defaultCharsets['armscii8'] = charsets[32];
defaultCharsets['utf8'] = charsets[33];
defaultCharsets['ucs2'] = charsets[35];
defaultCharsets['cp866'] = charsets[36];
defaultCharsets['keybcs2'] = charsets[37];
defaultCharsets['macce'] = charsets[38];
defaultCharsets['macroman'] = charsets[39];
defaultCharsets['cp852'] = charsets[40];
defaultCharsets['latin7'] = charsets[41];
defaultCharsets['utf8mb4'] = charsets[45];
defaultCharsets['cp1251'] = charsets[51];
defaultCharsets['utf16'] = charsets[54];
defaultCharsets['utf16le'] = charsets[56];
defaultCharsets['cp1256'] = charsets[57];
defaultCharsets['cp1257'] = charsets[59];
defaultCharsets['utf32'] = charsets[60];
defaultCharsets['binary'] = charsets[63];
defaultCharsets['geostd8'] = charsets[92];
defaultCharsets['cp932'] = charsets[95];
defaultCharsets['eucjpms'] = charsets[97];
defaultCharsets['gb18030'] = charsets[248];

module.exports = Collation;
