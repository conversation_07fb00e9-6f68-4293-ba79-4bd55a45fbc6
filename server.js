const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const db = require('./db');

// POST /register-student
router.post('/register-student', async (req, res) => {
  try {
    const {
      studentID,
      fullName,
      course,
      yearLevel,
      section,
      email,
      phoneNumber,
      password
    } = req.body;

    // ✅ Validate required fields
    if (!studentID || !fullName || !email || !password) {
      return res.status(400).json({
        error: 'Student ID, Full name, email, and password are required.'
      });
    }

    // ✅ Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // ✅ Insert query with default or fallback values
    const insertSql = `
      INSERT INTO Students (
        StudentID, FullName, Course, YearLevel, Section,
        Email, PhoneNumber, Password,
        EnrollmentStatus, AccountStatus
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'Active', 'Allowed')
    `;

    // ✅ Fallbacks for optional fields
    const courseSafe = course?.trim() || 'N/A';
    const sectionSafe = section?.trim() || 'N/A';
    const phoneSafe = phoneNumber?.trim() || 'N/A';
    const yearLevelSafe = !isNaN(yearLevel) ? parseInt(yearLevel) : 0;

    db.query(
      insertSql,
      [
        studentID,
        fullName,
        courseSafe,
        yearLevelSafe,
        sectionSafe,
        email,
        phoneSafe,
        hashedPassword
      ],
      (err, result) => {
        if (err) {
          // Catch duplicate key errors etc.
          return res.status(500).json({ error: err.message });
        }

        res.json({
          message: '✅ Student registered successfully',
          studentID
        });
      }
    );
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;



/*


PARA POSTING FORMAT 


http://localhost:3000/api/auth/register-student

{
  "studentID": "2022-00001",
  "fullName": "Nathaniel Inocando",
  "course": "BSIT",
  "yearLevel": 3,
  "section": "A",
  "email": "<EMAIL>",
  "phoneNumber": "09123456789",
  "password": "securePassword123"
}





*/