const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const db = require('../db'); // Your DB connection file

// ✅ Register student endpoint (manual or auto ID)
router.post('/register-student', async (req, res) => {
  try {
    let {
      studentID,  // <-- Now accepting studentID manually
      fullName,
      course,
      yearLevel,
      section,
      email,
      phoneNumber,
      password
    } = req.body;

    if (!fullName || !email || !password) {
      return res.status(400).json({ error: 'Full name, email, and password are required.' });
    }

    course = course?.trim() !== '' ? course : 'N/A';
    section = section?.trim() !== '' ? section : 'N/A';
    phoneNumber = phoneNumber?.trim() !== '' ? phoneNumber : 'N/A';
    yearLevel = yearLevel && !isNaN(yearLevel) ? parseInt(yearLevel) : 0;

    const hashedPassword = await bcrypt.hash(password, 10);

    // ✅ Use provided studentID or generate one
    const handleInsert = (idToUse) => {
      const insertQuery = `
        INSERT INTO Students (
          StudentID, FullName, Course, YearLevel, Section,
          Email, PhoneNumber, Password,
          EnrollmentStatus, AccountStatus
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'Active', 'Allowed')
      `;

      db.query(
        insertQuery,
        [idToUse, fullName, course, yearLevel, section, email, phoneNumber, hashedPassword],
        (insertErr, insertResult) => {
          if (insertErr) return res.status(500).json({ error: insertErr.message });

          res.json({
            message: '✅ Student registered successfully',
            studentID: idToUse
          });
        }
      );
    };

    if (studentID && studentID.trim() !== '') {
      // ✅ If provided, check if it already exists
      const checkQuery = `SELECT * FROM Students WHERE StudentID = ?`;
      db.query(checkQuery, [studentID], (err, results) => {
        if (err) return res.status(500).json({ error: err.message });
        if (results.length > 0) {
          return res.status(400).json({ error: '❌ StudentID already exists.' });
        }
        handleInsert(studentID); // Insert using provided ID
      });
    } else {
      // ✅ Auto-generate StudentID
      const getLastIDQuery = `SELECT StudentID FROM Students ORDER BY StudentID DESC LIMIT 1`;
      db.query(getLastIDQuery, (err, results) => {
        if (err) return res.status(500).json({ error: err.message });

        const currentYear = new Date().getFullYear();
        let newID;

        if (results.length === 0) {
          newID = `${currentYear}-00001`;
        } else {
          const lastID = results[0].StudentID;
          const [year, number] = lastID.split('-');
          const nextNumber = String(parseInt(number) + 1).padStart(5, '0');
          newID = `${year}-${nextNumber}`;
        }

        handleInsert(newID); // Insert using auto-generated ID
      });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// ✅ Delete student endpoint
router.delete('/delete-student/:studentID', (req, res) => {
  const { studentID } = req.params;

  const deleteQuery = `DELETE FROM Students WHERE StudentID = ?`;

  db.query(deleteQuery, [studentID], (err, result) => {
    if (err) return res.status(500).json({ error: err.message });

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: '❌ Student not found.' });
    }

    res.json({ message: `🗑️ Student with ID ${studentID} has been deleted.` });
  });
});

module.exports = router;

/*



example if mag butang kag posting

http://localhost:3000/api/auth/register-student

{
  "studentID": "2022-00143",
  "fullName": "Nathaniel Inocando",
  "course": "BSIT",
  "yearLevel": 4,
  "section": "",
  "email": "<EMAIL>",
  "phoneNumber": "09123456789",
  "password": "hellonathandev@"
}





*/