const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const db = require('../db');

// POST /register-student
router.post('/register-student', async (req, res) => {
  try {
    let {
      studentID,
      fullName,
      course,
      yearLevel,
      section,
      email,
      phoneNumber,
      password
    } = req.body;

    if (!fullName || !email || !password) {
      return res.status(400).json({ error: 'Full name, email, and password are required.' });
    }

    course = course?.trim() !== '' ? course : 'N/A';
    section = section?.trim() !== '' ? section : 'N/A';
    phoneNumber = phoneNumber?.trim() !== '' ? phoneNumber : 'N/A';
    yearLevel = yearLevel && !isNaN(yearLevel) ? parseInt(yearLevel) : 0;

    const hashedPassword = await bcrypt.hash(password, 10);
    const handleInsert = (idToUse) => {
      const insertQuery = `
        INSERT INTO Students (
          StudentID, FullName, Course, YearLevel, Section,
          Email, PhoneNumber, Password,
          EnrollmentStatus, AccountStatus
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'Active', 'Allowed')
      `;

      db.query(
        insertQuery,
        [idToUse, fullName, course, yearLevel, section, email, phoneNumber, hashedPassword],
        (insertErr) => {
          if (insertErr) return res.status(500).json({ error: insertErr.message });

          res.json({
            message: '✅ Student registered successfully',
            studentID: idToUse
          });
        }
      );
    };

    if (studentID && studentID.trim() !== '') {
      const checkQuery = `SELECT * FROM Students WHERE StudentID = ?`;
      db.query(checkQuery, [studentID], (err, results) => {
        if (err) return res.status(500).json({ error: err.message });
        if (results.length > 0) {
          return res.status(400).json({ error: '❌ StudentID already exists.' });
        }
        handleInsert(studentID);
      });
    } else {
      const getLastIDQuery = `SELECT StudentID FROM Students ORDER BY StudentID DESC LIMIT 1`;
      db.query(getLastIDQuery, (err, results) => {
        if (err) return res.status(500).json({ error: err.message });

        const currentYear = new Date().getFullYear();
        let newID;

        if (results.length === 0) {
          newID = `${currentYear}-00001`;
        } else {
          const lastID = results[0].StudentID;
          const [year, number] = lastID.split('-');
          const nextNumber = String(parseInt(number) + 1).padStart(5, '0');
          newID = `${year}-${nextNumber}`;
        }

        handleInsert(newID);
      });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// DELETE /delete-student/:studentID
router.delete('/delete-student/:studentID', (req, res) => {
  const { studentID } = req.params;

  const deleteQuery = `DELETE FROM Students WHERE StudentID = ?`;

  db.query(deleteQuery, [studentID], (err, result) => {
    if (err) return res.status(500).json({ error: err.message });

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: '❌ Student not found.' });
    }

    res.json({ message: `🗑️ Student with ID ${studentID} has been deleted.` });
  });
});

// GET /get-student/:studentID
router.get('/get-student/:studentID', (req, res) => {
  const { studentID } = req.params;

  const selectQuery = `SELECT * FROM Students WHERE StudentID = ?`;

  db.query(selectQuery, [studentID], (err, results) => {
    if (err) return res.status(500).json({ error: err.message });

    if (results.length === 0) {
      return res.status(404).json({ message: '❌ Student not found.' });
    }

    const student = results[0];
    delete student.Password;

    res.json({
      message: '✅ Student found',
      student: student
    });
  });
});

// GET /get-all-students
router.get('/get-all-students', (req, res) => {
  const selectQuery = `SELECT StudentID, FullName, Course, YearLevel, Section, Email, PhoneNumber, EnrollmentStatus, AccountStatus, CreatedAt, UpdatedAt FROM Students ORDER BY StudentID ASC`;

  db.query(selectQuery, (err, results) => {
    if (err) return res.status(500).json({ error: err.message });

    res.json({
      message: '✅ Students retrieved successfully',
      count: results.length,
      students: results
    });
  });
});

// PUT /update-student/:studentID
router.put('/update-student/:studentID', async (req, res) => {
  try {
    const { studentID } = req.params;
    let {
      fullName,
      course,
      yearLevel,
      section,
      email,
      phoneNumber,
      password,
      enrollmentStatus,
      accountStatus
    } = req.body;

    const checkQuery = `SELECT * FROM Students WHERE StudentID = ?`;
    db.query(checkQuery, [studentID], async (err, results) => {
      if (err) return res.status(500).json({ error: err.message });

      if (results.length === 0) {
        return res.status(404).json({ message: '❌ Student not found.' });
      }

      const currentStudent = results[0];

      fullName = fullName || currentStudent.FullName;
      course = course || currentStudent.Course;
      yearLevel = yearLevel !== undefined ? parseInt(yearLevel) : currentStudent.YearLevel;
      section = section || currentStudent.Section;
      email = email || currentStudent.Email;
      phoneNumber = phoneNumber || currentStudent.PhoneNumber;
      enrollmentStatus = enrollmentStatus || currentStudent.EnrollmentStatus;
      accountStatus = accountStatus || currentStudent.AccountStatus;

      let updateQuery = `
        UPDATE Students SET
        FullName = ?, Course = ?, YearLevel = ?, Section = ?,
        Email = ?, PhoneNumber = ?, EnrollmentStatus = ?, AccountStatus = ?
      `;
      let queryParams = [fullName, course, yearLevel, section, email, phoneNumber, enrollmentStatus, accountStatus];

      if (password) {
        const hashedPassword = await bcrypt.hash(password, 10);
        updateQuery += `, Password = ?`;
        queryParams.push(hashedPassword);
      }

      updateQuery += ` WHERE StudentID = ?`;
      queryParams.push(studentID);

      db.query(updateQuery, queryParams, (updateErr, updateResult) => {
        if (updateErr) return res.status(500).json({ error: updateErr.message });

        if (updateResult.affectedRows === 0) {
          return res.status(404).json({ message: '❌ Student not found.' });
        }

        res.json({
          message: '✅ Student updated successfully',
          studentID: studentID
        });
      });
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;

/*



PARA POSTING FORMAT

POST /register-student
http://localhost:3000/api/auth/register-student
{
  "studentID": "2022-00143",
  "fullName": "Nathaniel Inocando",
  "course": "BSIT",
  "yearLevel": 4,
  "section": "A",
  "email": "<EMAIL>",
  "phoneNumber": "09123456789",
  "password": "hellonathandev@"
}

DELETE /delete-student/:studentID
http://localhost:3000/api/auth/delete-student/2022-00143

GET /get-student/:studentID
http://localhost:3000/api/auth/get-student/2022-00143

GET /get-all-students
http://localhost:3000/api/auth/get-all-students

PUT /update-student/:studentID
http://localhost:3000/api/auth/update-student/2022-00143
{
  "fullName": "Nathaniel Updated",
  "course": "BSCS",
  "yearLevel": 3,
  "section": "B",
  "email": "<EMAIL>",
  "phoneNumber": "***********",
  "password": "newpassword123",
  "enrollmentStatus": "Active",
  "accountStatus": "Allowed"
}





*/