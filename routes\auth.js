const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const db = require('../db'); // Assumes you have db connection exported from here

// Register student endpoint
router.post('/register-student', async (req, res) => {
  try {
    let {
      fullName,
      course,
      yearLevel,
      section,
      email,
      phoneNumber,
      password
    } = req.body;

    // ✅ Validate required fields
    if (!fullName || !email || !password) {
      return res.status(400).json({ error: 'Full name, email, and password are required.' });
    }

    // ✅ Optional fields: default values if empty
    course = course && course.trim() !== '' ? course : 'N/A';
    section = section && section.trim() !== '' ? section : 'N/A';
    phoneNumber = phoneNumber && phoneNumber.trim() !== '' ? phoneNumber : 'N/A';
    yearLevel = yearLevel && !isNaN(yearLevel) ? parseInt(yearLevel) : 0;

    // ✅ Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // ✅ Insert into DB, let StudentID be auto-generated
    const sql = `
      INSERT INTO Students (
        FullName, Course, YearLevel, Section,
        Email, PhoneNumber, Password,
        EnrollmentStatus, AccountStatus
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'Active', 'Allowed')
    `;

    db.query(
      sql,
      [fullName, course, yearLevel, section, email, phoneNumber, hashedPassword],
      (err, result) => {
        if (err) return res.status(500).json({ error: err.message });

        // ✅ Send back the newly generated StudentID
        res.json({
          message: '✅ Student registered successfully',
          studentID: result.insertId
        });
      }
    );
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;


/*




  example if mag butang kag fields. POSTING


  http://localhost:3000/api/auth/register-student

{
  "fullName": "Nathaniel Inocando",
  "email": "<EMAIL>",
  "password": "Secure123",
  "course": "BSIT",
  "yearLevel": 4,
  "section": "A",
  "phoneNumber": "***********"
}


*/