const mysql = require('mysql2');
require('dotenv').config();

console.log('🔍 Testing database connection...');
console.log('📋 Configuration:');
console.log(`   Host: ${process.env.DB_HOST || 'localhost'}`);
console.log(`   User: ${process.env.DB_USER || 'root'}`);
console.log(`   Database: ${process.env.DB_NAME || 'dblibrary'}`);
console.log('');

const db = mysql.createConnection({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASS || '',
  database: process.env.DB_NAME || 'dblibrary'
});

db.connect((err) => {
  if (err) {
    console.error('❌ Connection failed:', err.message);
    console.log('');
    console.log('💡 Troubleshooting steps:');
    console.log('1. Check if MySQL/MariaDB is running');
    console.log('2. Verify credentials in .env file');
    console.log('3. Ensure database exists');
    console.log('4. Check user permissions');
    console.log('');
    console.log('🔧 Quick fixes:');
    console.log('- Try using root user temporarily');
    console.log('- Create the database and user (see DATABASE_SETUP.md)');
    console.log('- Check MySQL service status');
    process.exit(1);
  }

  console.log('✅ Database connection successful!');
  
  // Test if Students table exists
  db.query('SHOW TABLES LIKE "Students"', (err, results) => {
    if (err) {
      console.error('❌ Error checking tables:', err.message);
    } else if (results.length === 0) {
      console.log('⚠️  Students table not found');
      console.log('💡 Run the SQL commands in DATABASE_SETUP.md to create the table');
    } else {
      console.log('✅ Students table exists');
      
      // Test table structure
      db.query('DESCRIBE Students', (err, results) => {
        if (err) {
          console.error('❌ Error describing table:', err.message);
        } else {
          console.log('📋 Students table structure:');
          results.forEach(column => {
            console.log(`   ${column.Field}: ${column.Type} ${column.Null === 'NO' ? '(Required)' : '(Optional)'}`);
          });
        }
        
        db.end();
        console.log('');
        console.log('🎉 Database test completed successfully!');
        console.log('🚀 You can now start the server with: npm start');
      });
    }
  });
});
