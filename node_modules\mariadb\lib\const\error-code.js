//  SPDX-License-Identifier: LGPL-2.1-or-later
//  Copyright (c) 2015-2024 MariaDB Corporation Ab

'use strict';

/**
 * File generated using test/tools/generate-mariadb.js
 * from MariaDB 10.9
 *
 * !!!!!! DO NOT CHANGE MANUALLY !!!!!!
 */

let codes = {};
codes[120] = 'HA_ERR_KEY_NOT_FOUND';
codes[121] = 'HA_ERR_FOUND_DUPP_KEY';
codes[122] = 'HA_ERR_INTERNAL_ERROR';
codes[123] = 'HA_ERR_RECORD_CHANGED';
codes[124] = 'HA_ERR_WRONG_INDEX';
codes[126] = 'HA_ERR_CRASHED';
codes[127] = 'HA_ERR_WRONG_IN_RECORD';
codes[128] = 'HA_ERR_OUT_OF_MEM';
codes[130] = 'HA_ERR_NOT_A_TABLE';
codes[131] = 'HA_ERR_WRONG_COMMAND';
codes[132] = 'HA_ERR_OLD_FILE';
codes[133] = 'HA_ERR_NO_ACTIVE_RECORD';
codes[134] = 'HA_ERR_RECORD_DELETED';
codes[135] = 'HA_ERR_RECORD_FILE_FULL';
codes[136] = 'HA_ERR_INDEX_FILE_FULL';
codes[137] = 'HA_ERR_END_OF_FILE';
codes[138] = 'HA_ERR_UNSUPPORTED';
codes[139] = 'HA_ERR_TO_BIG_ROW';
codes[140] = 'HA_WRONG_CREATE_OPTION';
codes[141] = 'HA_ERR_FOUND_DUPP_UNIQUE';
codes[142] = 'HA_ERR_UNKNOWN_CHARSET';
codes[143] = 'HA_ERR_WRONG_MRG_TABLE_DEF';
codes[144] = 'HA_ERR_CRASHED_ON_REPAIR';
codes[145] = 'HA_ERR_CRASHED_ON_USAGE';
codes[146] = 'HA_ERR_LOCK_WAIT_TIMEOUT';
codes[147] = 'HA_ERR_LOCK_TABLE_FULL';
codes[148] = 'HA_ERR_READ_ONLY_TRANSACTION';
codes[149] = 'HA_ERR_LOCK_DEADLOCK';
codes[150] = 'HA_ERR_CANNOT_ADD_FOREIGN';
codes[151] = 'HA_ERR_NO_REFERENCED_ROW';
codes[152] = 'HA_ERR_ROW_IS_REFERENCED';
codes[153] = 'HA_ERR_NO_SAVEPOINT';
codes[154] = 'HA_ERR_NON_UNIQUE_BLOCK_SIZE';
codes[155] = 'HA_ERR_NO_SUCH_TABLE';
codes[156] = 'HA_ERR_TABLE_EXIST';
codes[157] = 'HA_ERR_NO_CONNECTION';
codes[158] = 'HA_ERR_NULL_IN_SPATIAL';
codes[159] = 'HA_ERR_TABLE_DEF_CHANGED';
codes[160] = 'HA_ERR_NO_PARTITION_FOUND';
codes[161] = 'HA_ERR_RBR_LOGGING_FAILED';
codes[162] = 'HA_ERR_DROP_INDEX_FK';
codes[163] = 'HA_ERR_FOREIGN_DUPLICATE_KEY';
codes[164] = 'HA_ERR_TABLE_NEEDS_UPGRADE';
codes[165] = 'HA_ERR_TABLE_READONLY';
codes[166] = 'HA_ERR_AUTOINC_READ_FAILED';
codes[167] = 'HA_ERR_AUTOINC_ERANGE';
codes[168] = 'HA_ERR_GENERIC';
codes[169] = 'HA_ERR_RECORD_IS_THE_SAME';
codes[170] = 'HA_ERR_LOGGING_IMPOSSIBLE';
codes[171] = 'HA_ERR_CORRUPT_EVENT';
codes[172] = 'HA_ERR_NEW_FILE';
codes[173] = 'HA_ERR_ROWS_EVENT_APPLY';
codes[174] = 'HA_ERR_INITIALIZATION';
codes[175] = 'HA_ERR_FILE_TOO_SHORT';
codes[176] = 'HA_ERR_WRONG_CRC';
codes[177] = 'HA_ERR_TOO_MANY_CONCURRENT_TRXS';
codes[178] = 'HA_ERR_NOT_IN_LOCK_PARTITIONS';
codes[179] = 'HA_ERR_INDEX_COL_TOO_LONG';
codes[180] = 'HA_ERR_INDEX_CORRUPT';
codes[181] = 'HA_ERR_UNDO_REC_TOO_BIG';
codes[182] = 'HA_FTS_INVALID_DOCID';
codes[184] = 'HA_ERR_TABLESPACE_EXISTS';
codes[185] = 'HA_ERR_TOO_MANY_FIELDS';
codes[186] = 'HA_ERR_ROW_IN_WRONG_PARTITION';
codes[187] = 'HA_ERR_ROW_NOT_VISIBLE';
codes[188] = 'HA_ERR_ABORTED_BY_USER';
codes[189] = 'HA_ERR_DISK_FULL';
codes[190] = 'HA_ERR_INCOMPATIBLE_DEFINITION';
codes[191] = 'HA_ERR_FTS_TOO_MANY_WORDS_IN_PHRASE';
codes[192] = 'HA_ERR_DECRYPTION_FAILED';
codes[193] = 'HA_ERR_FK_DEPTH_EXCEEDED';
codes[194] = 'HA_ERR_TABLESPACE_MISSING';
codes[195] = 'HA_ERR_SEQUENCE_INVALID_DATA';
codes[196] = 'HA_ERR_SEQUENCE_RUN_OUT';
codes[197] = 'HA_ERR_COMMIT_ERROR';
codes[198] = 'HA_ERR_PARTITION_LIST';
codes[1000] = 'ER_HASHCHK';
codes[1001] = 'ER_NISAMCHK';
codes[1002] = 'ER_NO';
codes[1003] = 'ER_YES';
codes[1004] = 'ER_CANT_CREATE_FILE';
codes[1005] = 'ER_CANT_CREATE_TABLE';
codes[1006] = 'ER_CANT_CREATE_DB';
codes[1007] = 'ER_DB_CREATE_EXISTS';
codes[1008] = 'ER_DB_DROP_EXISTS';
codes[1009] = 'ER_DB_DROP_DELETE';
codes[1010] = 'ER_DB_DROP_RMDIR';
codes[1011] = 'ER_CANT_DELETE_FILE';
codes[1012] = 'ER_CANT_FIND_SYSTEM_REC';
codes[1013] = 'ER_CANT_GET_STAT';
codes[1014] = 'ER_CANT_GET_WD';
codes[1015] = 'ER_CANT_LOCK';
codes[1016] = 'ER_CANT_OPEN_FILE';
codes[1017] = 'ER_FILE_NOT_FOUND';
codes[1018] = 'ER_CANT_READ_DIR';
codes[1019] = 'ER_CANT_SET_WD';
codes[1020] = 'ER_CHECKREAD';
codes[1021] = 'ER_DISK_FULL';
codes[1022] = 'ER_DUP_KEY';
codes[1023] = 'ER_ERROR_ON_CLOSE';
codes[1024] = 'ER_ERROR_ON_READ';
codes[1025] = 'ER_ERROR_ON_RENAME';
codes[1026] = 'ER_ERROR_ON_WRITE';
codes[1027] = 'ER_FILE_USED';
codes[1028] = 'ER_FILSORT_ABORT';
codes[1029] = 'ER_FORM_NOT_FOUND';
codes[1030] = 'ER_GET_ERRNO';
codes[1031] = 'ER_ILLEGAL_HA';
codes[1032] = 'ER_KEY_NOT_FOUND';
codes[1033] = 'ER_NOT_FORM_FILE';
codes[1034] = 'ER_NOT_KEYFILE';
codes[1035] = 'ER_OLD_KEYFILE';
codes[1036] = 'ER_OPEN_AS_READONLY';
codes[1037] = 'ER_OUTOFMEMORY';
codes[1038] = 'ER_OUT_OF_SORTMEMORY';
codes[1039] = 'ER_UNEXPECTED_EOF';
codes[1040] = 'ER_CON_COUNT_ERROR';
codes[1041] = 'ER_OUT_OF_RESOURCES';
codes[1042] = 'ER_BAD_HOST_ERROR';
codes[1043] = 'ER_HANDSHAKE_ERROR';
codes[1044] = 'ER_DBACCESS_DENIED_ERROR';
codes[1045] = 'ER_ACCESS_DENIED_ERROR';
codes[1046] = 'ER_NO_DB_ERROR';
codes[1047] = 'ER_UNKNOWN_COM_ERROR';
codes[1048] = 'ER_BAD_NULL_ERROR';
codes[1049] = 'ER_BAD_DB_ERROR';
codes[1050] = 'ER_TABLE_EXISTS_ERROR';
codes[1051] = 'ER_BAD_TABLE_ERROR';
codes[1052] = 'ER_NON_UNIQ_ERROR';
codes[1053] = 'ER_SERVER_SHUTDOWN';
codes[1054] = 'ER_BAD_FIELD_ERROR';
codes[1055] = 'ER_WRONG_FIELD_WITH_GROUP';
codes[1056] = 'ER_WRONG_GROUP_FIELD';
codes[1057] = 'ER_WRONG_SUM_SELECT';
codes[1058] = 'ER_WRONG_VALUE_COUNT';
codes[1059] = 'ER_TOO_LONG_IDENT';
codes[1060] = 'ER_DUP_FIELDNAME';
codes[1061] = 'ER_DUP_KEYNAME';
codes[1062] = 'ER_DUP_ENTRY';
codes[1063] = 'ER_WRONG_FIELD_SPEC';
codes[1064] = 'ER_PARSE_ERROR';
codes[1065] = 'ER_EMPTY_QUERY';
codes[1066] = 'ER_NONUNIQ_TABLE';
codes[1067] = 'ER_INVALID_DEFAULT';
codes[1068] = 'ER_MULTIPLE_PRI_KEY';
codes[1069] = 'ER_TOO_MANY_KEYS';
codes[1070] = 'ER_TOO_MANY_KEY_PARTS';
codes[1071] = 'ER_TOO_LONG_KEY';
codes[1072] = 'ER_KEY_COLUMN_DOES_NOT_EXIST';
codes[1073] = 'ER_BLOB_USED_AS_KEY';
codes[1074] = 'ER_TOO_BIG_FIELDLENGTH';
codes[1075] = 'ER_WRONG_AUTO_KEY';
codes[1076] = 'ER_BINLOG_CANT_DELETE_GTID_DOMAIN';
codes[1077] = 'ER_NORMAL_SHUTDOWN';
codes[1078] = 'ER_GOT_SIGNAL';
codes[1079] = 'ER_SHUTDOWN_COMPLETE';
codes[1080] = 'ER_FORCING_CLOSE';
codes[1081] = 'ER_IPSOCK_ERROR';
codes[1082] = 'ER_NO_SUCH_INDEX';
codes[1083] = 'ER_WRONG_FIELD_TERMINATORS';
codes[1084] = 'ER_BLOBS_AND_NO_TERMINATED';
codes[1085] = 'ER_TEXTFILE_NOT_READABLE';
codes[1086] = 'ER_FILE_EXISTS_ERROR';
codes[1087] = 'ER_LOAD_INFO';
codes[1088] = 'ER_ALTER_INFO';
codes[1089] = 'ER_WRONG_SUB_KEY';
codes[1090] = 'ER_CANT_REMOVE_ALL_FIELDS';
codes[1091] = 'ER_CANT_DROP_FIELD_OR_KEY';
codes[1092] = 'ER_INSERT_INFO';
codes[1093] = 'ER_UPDATE_TABLE_USED';
codes[1094] = 'ER_NO_SUCH_THREAD';
codes[1095] = 'ER_KILL_DENIED_ERROR';
codes[1096] = 'ER_NO_TABLES_USED';
codes[1097] = 'ER_TOO_BIG_SET';
codes[1098] = 'ER_NO_UNIQUE_LOGFILE';
codes[1099] = 'ER_TABLE_NOT_LOCKED_FOR_WRITE';
codes[1100] = 'ER_TABLE_NOT_LOCKED';
codes[1101] = 'ER_UNUSED_17';
codes[1102] = 'ER_WRONG_DB_NAME';
codes[1103] = 'ER_WRONG_TABLE_NAME';
codes[1104] = 'ER_TOO_BIG_SELECT';
codes[1105] = 'ER_UNKNOWN_ERROR';
codes[1106] = 'ER_UNKNOWN_PROCEDURE';
codes[1107] = 'ER_WRONG_PARAMCOUNT_TO_PROCEDURE';
codes[1108] = 'ER_WRONG_PARAMETERS_TO_PROCEDURE';
codes[1109] = 'ER_UNKNOWN_TABLE';
codes[1110] = 'ER_FIELD_SPECIFIED_TWICE';
codes[1111] = 'ER_INVALID_GROUP_FUNC_USE';
codes[1112] = 'ER_UNSUPPORTED_EXTENSION';
codes[1113] = 'ER_TABLE_MUST_HAVE_COLUMNS';
codes[1114] = 'ER_RECORD_FILE_FULL';
codes[1115] = 'ER_UNKNOWN_CHARACTER_SET';
codes[1116] = 'ER_TOO_MANY_TABLES';
codes[1117] = 'ER_TOO_MANY_FIELDS';
codes[1118] = 'ER_TOO_BIG_ROWSIZE';
codes[1119] = 'ER_STACK_OVERRUN';
codes[1120] = 'ER_WRONG_OUTER_JOIN';
codes[1121] = 'ER_NULL_COLUMN_IN_INDEX';
codes[1122] = 'ER_CANT_FIND_UDF';
codes[1123] = 'ER_CANT_INITIALIZE_UDF';
codes[1124] = 'ER_UDF_NO_PATHS';
codes[1125] = 'ER_UDF_EXISTS';
codes[1126] = 'ER_CANT_OPEN_LIBRARY';
codes[1127] = 'ER_CANT_FIND_DL_ENTRY';
codes[1128] = 'ER_FUNCTION_NOT_DEFINED';
codes[1129] = 'ER_HOST_IS_BLOCKED';
codes[1130] = 'ER_HOST_NOT_PRIVILEGED';
codes[1131] = 'ER_PASSWORD_ANONYMOUS_USER';
codes[1132] = 'ER_PASSWORD_NOT_ALLOWED';
codes[1133] = 'ER_PASSWORD_NO_MATCH';
codes[1134] = 'ER_UPDATE_INFO';
codes[1135] = 'ER_CANT_CREATE_THREAD';
codes[1136] = 'ER_WRONG_VALUE_COUNT_ON_ROW';
codes[1137] = 'ER_CANT_REOPEN_TABLE';
codes[1138] = 'ER_INVALID_USE_OF_NULL';
codes[1139] = 'ER_REGEXP_ERROR';
codes[1140] = 'ER_MIX_OF_GROUP_FUNC_AND_FIELDS';
codes[1141] = 'ER_NONEXISTING_GRANT';
codes[1142] = 'ER_TABLEACCESS_DENIED_ERROR';
codes[1143] = 'ER_COLUMNACCESS_DENIED_ERROR';
codes[1144] = 'ER_ILLEGAL_GRANT_FOR_TABLE';
codes[1145] = 'ER_GRANT_WRONG_HOST_OR_USER';
codes[1146] = 'ER_NO_SUCH_TABLE';
codes[1147] = 'ER_NONEXISTING_TABLE_GRANT';
codes[1148] = 'ER_NOT_ALLOWED_COMMAND';
codes[1149] = 'ER_SYNTAX_ERROR';
codes[1150] = 'ER_DELAYED_CANT_CHANGE_LOCK';
codes[1151] = 'ER_TOO_MANY_DELAYED_THREADS';
codes[1152] = 'ER_ABORTING_CONNECTION';
codes[1153] = 'ER_NET_PACKET_TOO_LARGE';
codes[1154] = 'ER_NET_READ_ERROR_FROM_PIPE';
codes[1155] = 'ER_NET_FCNTL_ERROR';
codes[1156] = 'ER_NET_PACKETS_OUT_OF_ORDER';
codes[1157] = 'ER_NET_UNCOMPRESS_ERROR';
codes[1158] = 'ER_NET_READ_ERROR';
codes[1159] = 'ER_NET_READ_INTERRUPTED';
codes[1160] = 'ER_NET_ERROR_ON_WRITE';
codes[1161] = 'ER_NET_WRITE_INTERRUPTED';
codes[1162] = 'ER_TOO_LONG_STRING';
codes[1163] = 'ER_TABLE_CANT_HANDLE_BLOB';
codes[1164] = 'ER_TABLE_CANT_HANDLE_AUTO_INCREMENT';
codes[1165] = 'ER_DELAYED_INSERT_TABLE_LOCKED';
codes[1166] = 'ER_WRONG_COLUMN_NAME';
codes[1167] = 'ER_WRONG_KEY_COLUMN';
codes[1168] = 'ER_WRONG_MRG_TABLE';
codes[1169] = 'ER_DUP_UNIQUE';
codes[1170] = 'ER_BLOB_KEY_WITHOUT_LENGTH';
codes[1171] = 'ER_PRIMARY_CANT_HAVE_NULL';
codes[1172] = 'ER_TOO_MANY_ROWS';
codes[1173] = 'ER_REQUIRES_PRIMARY_KEY';
codes[1174] = 'ER_NO_RAID_COMPILED';
codes[1175] = 'ER_UPDATE_WITHOUT_KEY_IN_SAFE_MODE';
codes[1176] = 'ER_KEY_DOES_NOT_EXISTS';
codes[1177] = 'ER_CHECK_NO_SUCH_TABLE';
codes[1178] = 'ER_CHECK_NOT_IMPLEMENTED';
codes[1179] = 'ER_CANT_DO_THIS_DURING_AN_TRANSACTION';
codes[1180] = 'ER_ERROR_DURING_COMMIT';
codes[1181] = 'ER_ERROR_DURING_ROLLBACK';
codes[1182] = 'ER_ERROR_DURING_FLUSH_LOGS';
codes[1183] = 'ER_ERROR_DURING_CHECKPOINT';
codes[1184] = 'ER_NEW_ABORTING_CONNECTION';
codes[1185] = 'ER_UNUSED_10';
codes[1186] = 'ER_FLUSH_MASTER_BINLOG_CLOSED';
codes[1187] = 'ER_INDEX_REBUILD';
codes[1188] = 'ER_MASTER';
codes[1189] = 'ER_MASTER_NET_READ';
codes[1190] = 'ER_MASTER_NET_WRITE';
codes[1191] = 'ER_FT_MATCHING_KEY_NOT_FOUND';
codes[1192] = 'ER_LOCK_OR_ACTIVE_TRANSACTION';
codes[1193] = 'ER_UNKNOWN_SYSTEM_VARIABLE';
codes[1194] = 'ER_CRASHED_ON_USAGE';
codes[1195] = 'ER_CRASHED_ON_REPAIR';
codes[1196] = 'ER_WARNING_NOT_COMPLETE_ROLLBACK';
codes[1197] = 'ER_TRANS_CACHE_FULL';
codes[1198] = 'ER_SLAVE_MUST_STOP';
codes[1199] = 'ER_SLAVE_NOT_RUNNING';
codes[1200] = 'ER_BAD_SLAVE';
codes[1201] = 'ER_MASTER_INFO';
codes[1202] = 'ER_SLAVE_THREAD';
codes[1203] = 'ER_TOO_MANY_USER_CONNECTIONS';
codes[1204] = 'ER_SET_CONSTANTS_ONLY';
codes[1205] = 'ER_LOCK_WAIT_TIMEOUT';
codes[1206] = 'ER_LOCK_TABLE_FULL';
codes[1207] = 'ER_READ_ONLY_TRANSACTION';
codes[1208] = 'ER_DROP_DB_WITH_READ_LOCK';
codes[1209] = 'ER_CREATE_DB_WITH_READ_LOCK';
codes[1210] = 'ER_WRONG_ARGUMENTS';
codes[1211] = 'ER_NO_PERMISSION_TO_CREATE_USER';
codes[1212] = 'ER_UNION_TABLES_IN_DIFFERENT_DIR';
codes[1213] = 'ER_LOCK_DEADLOCK';
codes[1214] = 'ER_TABLE_CANT_HANDLE_FT';
codes[1215] = 'ER_CANNOT_ADD_FOREIGN';
codes[1216] = 'ER_NO_REFERENCED_ROW';
codes[1217] = 'ER_ROW_IS_REFERENCED';
codes[1218] = 'ER_CONNECT_TO_MASTER';
codes[1219] = 'ER_QUERY_ON_MASTER';
codes[1220] = 'ER_ERROR_WHEN_EXECUTING_COMMAND';
codes[1221] = 'ER_WRONG_USAGE';
codes[1222] = 'ER_WRONG_NUMBER_OF_COLUMNS_IN_SELECT';
codes[1223] = 'ER_CANT_UPDATE_WITH_READLOCK';
codes[1224] = 'ER_MIXING_NOT_ALLOWED';
codes[1225] = 'ER_DUP_ARGUMENT';
codes[1226] = 'ER_USER_LIMIT_REACHED';
codes[1227] = 'ER_SPECIFIC_ACCESS_DENIED_ERROR';
codes[1228] = 'ER_LOCAL_VARIABLE';
codes[1229] = 'ER_GLOBAL_VARIABLE';
codes[1230] = 'ER_NO_DEFAULT';
codes[1231] = 'ER_WRONG_VALUE_FOR_VAR';
codes[1232] = 'ER_WRONG_TYPE_FOR_VAR';
codes[1233] = 'ER_VAR_CANT_BE_READ';
codes[1234] = 'ER_CANT_USE_OPTION_HERE';
codes[1235] = 'ER_NOT_SUPPORTED_YET';
codes[1236] = 'ER_MASTER_FATAL_ERROR_READING_BINLOG';
codes[1237] = 'ER_SLAVE_IGNORED_TABLE';
codes[1238] = 'ER_INCORRECT_GLOBAL_LOCAL_VAR';
codes[1239] = 'ER_WRONG_FK_DEF';
codes[1240] = 'ER_KEY_REF_DO_NOT_MATCH_TABLE_REF';
codes[1241] = 'ER_OPERAND_COLUMNS';
codes[1242] = 'ER_SUBQUERY_NO_1_ROW';
codes[1243] = 'ER_UNKNOWN_STMT_HANDLER';
codes[1244] = 'ER_CORRUPT_HELP_DB';
codes[1245] = 'ER_CYCLIC_REFERENCE';
codes[1246] = 'ER_AUTO_CONVERT';
codes[1247] = 'ER_ILLEGAL_REFERENCE';
codes[1248] = 'ER_DERIVED_MUST_HAVE_ALIAS';
codes[1249] = 'ER_SELECT_REDUCED';
codes[1250] = 'ER_TABLENAME_NOT_ALLOWED_HERE';
codes[1251] = 'ER_NOT_SUPPORTED_AUTH_MODE';
codes[1252] = 'ER_SPATIAL_CANT_HAVE_NULL';
codes[1253] = 'ER_COLLATION_CHARSET_MISMATCH';
codes[1254] = 'ER_SLAVE_WAS_RUNNING';
codes[1255] = 'ER_SLAVE_WAS_NOT_RUNNING';
codes[1256] = 'ER_TOO_BIG_FOR_UNCOMPRESS';
codes[1257] = 'ER_ZLIB_Z_MEM_ERROR';
codes[1258] = 'ER_ZLIB_Z_BUF_ERROR';
codes[1259] = 'ER_ZLIB_Z_DATA_ERROR';
codes[1260] = 'ER_CUT_VALUE_GROUP_CONCAT';
codes[1261] = 'ER_WARN_TOO_FEW_RECORDS';
codes[1262] = 'ER_WARN_TOO_MANY_RECORDS';
codes[1263] = 'ER_WARN_NULL_TO_NOTNULL';
codes[1264] = 'ER_WARN_DATA_OUT_OF_RANGE';
codes[1265] = 'WARN_DATA_TRUNCATED';
codes[1266] = 'ER_WARN_USING_OTHER_HANDLER';
codes[1267] = 'ER_CANT_AGGREGATE_2COLLATIONS';
codes[1268] = 'ER_DROP_USER';
codes[1269] = 'ER_REVOKE_GRANTS';
codes[1270] = 'ER_CANT_AGGREGATE_3COLLATIONS';
codes[1271] = 'ER_CANT_AGGREGATE_NCOLLATIONS';
codes[1272] = 'ER_VARIABLE_IS_NOT_STRUCT';
codes[1273] = 'ER_UNKNOWN_COLLATION';
codes[1274] = 'ER_SLAVE_IGNORED_SSL_PARAMS';
codes[1275] = 'ER_SERVER_IS_IN_SECURE_AUTH_MODE';
codes[1276] = 'ER_WARN_FIELD_RESOLVED';
codes[1277] = 'ER_BAD_SLAVE_UNTIL_COND';
codes[1278] = 'ER_MISSING_SKIP_SLAVE';
codes[1279] = 'ER_UNTIL_COND_IGNORED';
codes[1280] = 'ER_WRONG_NAME_FOR_INDEX';
codes[1281] = 'ER_WRONG_NAME_FOR_CATALOG';
codes[1282] = 'ER_WARN_QC_RESIZE';
codes[1283] = 'ER_BAD_FT_COLUMN';
codes[1284] = 'ER_UNKNOWN_KEY_CACHE';
codes[1285] = 'ER_WARN_HOSTNAME_WONT_WORK';
codes[1286] = 'ER_UNKNOWN_STORAGE_ENGINE';
codes[1287] = 'ER_WARN_DEPRECATED_SYNTAX';
codes[1288] = 'ER_NON_UPDATABLE_TABLE';
codes[1289] = 'ER_FEATURE_DISABLED';
codes[1290] = 'ER_OPTION_PREVENTS_STATEMENT';
codes[1291] = 'ER_DUPLICATED_VALUE_IN_TYPE';
codes[1292] = 'ER_TRUNCATED_WRONG_VALUE';
codes[1293] = 'ER_TOO_MUCH_AUTO_TIMESTAMP_COLS';
codes[1294] = 'ER_INVALID_ON_UPDATE';
codes[1295] = 'ER_UNSUPPORTED_PS';
codes[1296] = 'ER_GET_ERRMSG';
codes[1297] = 'ER_GET_TEMPORARY_ERRMSG';
codes[1298] = 'ER_UNKNOWN_TIME_ZONE';
codes[1299] = 'ER_WARN_INVALID_TIMESTAMP';
codes[1300] = 'ER_INVALID_CHARACTER_STRING';
codes[1301] = 'ER_WARN_ALLOWED_PACKET_OVERFLOWED';
codes[1302] = 'ER_CONFLICTING_DECLARATIONS';
codes[1303] = 'ER_SP_NO_RECURSIVE_CREATE';
codes[1304] = 'ER_SP_ALREADY_EXISTS';
codes[1305] = 'ER_SP_DOES_NOT_EXIST';
codes[1306] = 'ER_SP_DROP_FAILED';
codes[1307] = 'ER_SP_STORE_FAILED';
codes[1308] = 'ER_SP_LILABEL_MISMATCH';
codes[1309] = 'ER_SP_LABEL_REDEFINE';
codes[1310] = 'ER_SP_LABEL_MISMATCH';
codes[1311] = 'ER_SP_UNINIT_VAR';
codes[1312] = 'ER_SP_BADSELECT';
codes[1313] = 'ER_SP_BADRETURN';
codes[1314] = 'ER_SP_BADSTATEMENT';
codes[1315] = 'ER_UPDATE_LOG_DEPRECATED_IGNORED';
codes[1316] = 'ER_UPDATE_LOG_DEPRECATED_TRANSLATED';
codes[1317] = 'ER_QUERY_INTERRUPTED';
codes[1318] = 'ER_SP_WRONG_NO_OF_ARGS';
codes[1319] = 'ER_SP_COND_MISMATCH';
codes[1320] = 'ER_SP_NORETURN';
codes[1321] = 'ER_SP_NORETURNEND';
codes[1322] = 'ER_SP_BAD_CURSOR_QUERY';
codes[1323] = 'ER_SP_BAD_CURSOR_SELECT';
codes[1324] = 'ER_SP_CURSOR_MISMATCH';
codes[1325] = 'ER_SP_CURSOR_ALREADY_OPEN';
codes[1326] = 'ER_SP_CURSOR_NOT_OPEN';
codes[1327] = 'ER_SP_UNDECLARED_VAR';
codes[1328] = 'ER_SP_WRONG_NO_OF_FETCH_ARGS';
codes[1329] = 'ER_SP_FETCH_NO_DATA';
codes[1330] = 'ER_SP_DUP_PARAM';
codes[1331] = 'ER_SP_DUP_VAR';
codes[1332] = 'ER_SP_DUP_COND';
codes[1333] = 'ER_SP_DUP_CURS';
codes[1334] = 'ER_SP_CANT_ALTER';
codes[1335] = 'ER_SP_SUBSELECT_NYI';
codes[1336] = 'ER_STMT_NOT_ALLOWED_IN_SF_OR_TRG';
codes[1337] = 'ER_SP_VARCOND_AFTER_CURSHNDLR';
codes[1338] = 'ER_SP_CURSOR_AFTER_HANDLER';
codes[1339] = 'ER_SP_CASE_NOT_FOUND';
codes[1340] = 'ER_FPARSER_TOO_BIG_FILE';
codes[1341] = 'ER_FPARSER_BAD_HEADER';
codes[1342] = 'ER_FPARSER_EOF_IN_COMMENT';
codes[1343] = 'ER_FPARSER_ERROR_IN_PARAMETER';
codes[1344] = 'ER_FPARSER_EOF_IN_UNKNOWN_PARAMETER';
codes[1345] = 'ER_VIEW_NO_EXPLAIN';
codes[1346] = 'ER_FRM_UNKNOWN_TYPE';
codes[1347] = 'ER_WRONG_OBJECT';
codes[1348] = 'ER_NONUPDATEABLE_COLUMN';
codes[1349] = 'ER_VIEW_SELECT_DERIVED';
codes[1350] = 'ER_VIEW_SELECT_CLAUSE';
codes[1351] = 'ER_VIEW_SELECT_VARIABLE';
codes[1352] = 'ER_VIEW_SELECT_TMPTABLE';
codes[1353] = 'ER_VIEW_WRONG_LIST';
codes[1354] = 'ER_WARN_VIEW_MERGE';
codes[1355] = 'ER_WARN_VIEW_WITHOUT_KEY';
codes[1356] = 'ER_VIEW_INVALID';
codes[1357] = 'ER_SP_NO_DROP_SP';
codes[1358] = 'ER_SP_GOTO_IN_HNDLR';
codes[1359] = 'ER_TRG_ALREADY_EXISTS';
codes[1360] = 'ER_TRG_DOES_NOT_EXIST';
codes[1361] = 'ER_TRG_ON_VIEW_OR_TEMP_TABLE';
codes[1362] = 'ER_TRG_CANT_CHANGE_ROW';
codes[1363] = 'ER_TRG_NO_SUCH_ROW_IN_TRG';
codes[1364] = 'ER_NO_DEFAULT_FOR_FIELD';
codes[1365] = 'ER_DIVISION_BY_ZERO';
codes[1366] = 'ER_TRUNCATED_WRONG_VALUE_FOR_FIELD';
codes[1367] = 'ER_ILLEGAL_VALUE_FOR_TYPE';
codes[1368] = 'ER_VIEW_NONUPD_CHECK';
codes[1369] = 'ER_VIEW_CHECK_FAILED';
codes[1370] = 'ER_PROCACCESS_DENIED_ERROR';
codes[1371] = 'ER_RELAY_LOG_FAIL';
codes[1372] = 'ER_PASSWD_LENGTH';
codes[1373] = 'ER_UNKNOWN_TARGET_BINLOG';
codes[1374] = 'ER_IO_ERR_LOG_INDEX_READ';
codes[1375] = 'ER_BINLOG_PURGE_PROHIBITED';
codes[1376] = 'ER_FSEEK_FAIL';
codes[1377] = 'ER_BINLOG_PURGE_FATAL_ERR';
codes[1378] = 'ER_LOG_IN_USE';
codes[1379] = 'ER_LOG_PURGE_UNKNOWN_ERR';
codes[1380] = 'ER_RELAY_LOG_INIT';
codes[1381] = 'ER_NO_BINARY_LOGGING';
codes[1382] = 'ER_RESERVED_SYNTAX';
codes[1383] = 'ER_WSAS_FAILED';
codes[1384] = 'ER_DIFF_GROUPS_PROC';
codes[1385] = 'ER_NO_GROUP_FOR_PROC';
codes[1386] = 'ER_ORDER_WITH_PROC';
codes[1387] = 'ER_LOGGING_PROHIBIT_CHANGING_OF';
codes[1388] = 'ER_NO_FILE_MAPPING';
codes[1389] = 'ER_WRONG_MAGIC';
codes[1390] = 'ER_PS_MANY_PARAM';
codes[1391] = 'ER_KEY_PART_0';
codes[1392] = 'ER_VIEW_CHECKSUM';
codes[1393] = 'ER_VIEW_MULTIUPDATE';
codes[1394] = 'ER_VIEW_NO_INSERT_FIELD_LIST';
codes[1395] = 'ER_VIEW_DELETE_MERGE_VIEW';
codes[1396] = 'ER_CANNOT_USER';
codes[1397] = 'ER_XAER_NOTA';
codes[1398] = 'ER_XAER_INVAL';
codes[1399] = 'ER_XAER_RMFAIL';
codes[1400] = 'ER_XAER_OUTSIDE';
codes[1401] = 'ER_XAER_RMERR';
codes[1402] = 'ER_XA_RBROLLBACK';
codes[1403] = 'ER_NONEXISTING_PROC_GRANT';
codes[1404] = 'ER_PROC_AUTO_GRANT_FAIL';
codes[1405] = 'ER_PROC_AUTO_REVOKE_FAIL';
codes[1406] = 'ER_DATA_TOO_LONG';
codes[1407] = 'ER_SP_BAD_SQLSTATE';
codes[1408] = 'ER_STARTUP';
codes[1409] = 'ER_LOAD_FROM_FIXED_SIZE_ROWS_TO_VAR';
codes[1410] = 'ER_CANT_CREATE_USER_WITH_GRANT';
codes[1411] = 'ER_WRONG_VALUE_FOR_TYPE';
codes[1412] = 'ER_TABLE_DEF_CHANGED';
codes[1413] = 'ER_SP_DUP_HANDLER';
codes[1414] = 'ER_SP_NOT_VAR_ARG';
codes[1415] = 'ER_SP_NO_RETSET';
codes[1416] = 'ER_CANT_CREATE_GEOMETRY_OBJECT';
codes[1417] = 'ER_FAILED_ROUTINE_BREAK_BINLOG';
codes[1418] = 'ER_BINLOG_UNSAFE_ROUTINE';
codes[1419] = 'ER_BINLOG_CREATE_ROUTINE_NEED_SUPER';
codes[1420] = 'ER_EXEC_STMT_WITH_OPEN_CURSOR';
codes[1421] = 'ER_STMT_HAS_NO_OPEN_CURSOR';
codes[1422] = 'ER_COMMIT_NOT_ALLOWED_IN_SF_OR_TRG';
codes[1423] = 'ER_NO_DEFAULT_FOR_VIEW_FIELD';
codes[1424] = 'ER_SP_NO_RECURSION';
codes[1425] = 'ER_TOO_BIG_SCALE';
codes[1426] = 'ER_TOO_BIG_PRECISION';
codes[1427] = 'ER_M_BIGGER_THAN_D';
codes[1428] = 'ER_WRONG_LOCK_OF_SYSTEM_TABLE';
codes[1429] = 'ER_CONNECT_TO_FOREIGN_DATA_SOURCE';
codes[1430] = 'ER_QUERY_ON_FOREIGN_DATA_SOURCE';
codes[1431] = 'ER_FOREIGN_DATA_SOURCE_DOESNT_EXIST';
codes[1432] = 'ER_FOREIGN_DATA_STRING_INVALID_CANT_CREATE';
codes[1433] = 'ER_FOREIGN_DATA_STRING_INVALID';
codes[1434] = 'ER_CANT_CREATE_FEDERATED_TABLE';
codes[1435] = 'ER_TRG_IN_WRONG_SCHEMA';
codes[1436] = 'ER_STACK_OVERRUN_NEED_MORE';
codes[1437] = 'ER_TOO_LONG_BODY';
codes[1438] = 'ER_WARN_CANT_DROP_DEFAULT_KEYCACHE';
codes[1439] = 'ER_TOO_BIG_DISPLAYWIDTH';
codes[1440] = 'ER_XAER_DUPID';
codes[1441] = 'ER_DATETIME_FUNCTION_OVERFLOW';
codes[1442] = 'ER_CANT_UPDATE_USED_TABLE_IN_SF_OR_TRG';
codes[1443] = 'ER_VIEW_PREVENT_UPDATE';
codes[1444] = 'ER_PS_NO_RECURSION';
codes[1445] = 'ER_SP_CANT_SET_AUTOCOMMIT';
codes[1446] = 'ER_MALFORMED_DEFINER';
codes[1447] = 'ER_VIEW_FRM_NO_USER';
codes[1448] = 'ER_VIEW_OTHER_USER';
codes[1449] = 'ER_NO_SUCH_USER';
codes[1450] = 'ER_FORBID_SCHEMA_CHANGE';
codes[1451] = 'ER_ROW_IS_REFERENCED_2';
codes[1452] = 'ER_NO_REFERENCED_ROW_2';
codes[1453] = 'ER_SP_BAD_VAR_SHADOW';
codes[1454] = 'ER_TRG_NO_DEFINER';
codes[1455] = 'ER_OLD_FILE_FORMAT';
codes[1456] = 'ER_SP_RECURSION_LIMIT';
codes[1457] = 'ER_SP_PROC_TABLE_CORRUPT';
codes[1458] = 'ER_SP_WRONG_NAME';
codes[1459] = 'ER_TABLE_NEEDS_UPGRADE';
codes[1460] = 'ER_SP_NO_AGGREGATE';
codes[1461] = 'ER_MAX_PREPARED_STMT_COUNT_REACHED';
codes[1462] = 'ER_VIEW_RECURSIVE';
codes[1463] = 'ER_NON_GROUPING_FIELD_USED';
codes[1464] = 'ER_TABLE_CANT_HANDLE_SPKEYS';
codes[1465] = 'ER_NO_TRIGGERS_ON_SYSTEM_SCHEMA';
codes[1466] = 'ER_REMOVED_SPACES';
codes[1467] = 'ER_AUTOINC_READ_FAILED';
codes[1468] = 'ER_USERNAME';
codes[1469] = 'ER_HOSTNAME';
codes[1470] = 'ER_WRONG_STRING_LENGTH';
codes[1471] = 'ER_NON_INSERTABLE_TABLE';
codes[1472] = 'ER_ADMIN_WRONG_MRG_TABLE';
codes[1473] = 'ER_TOO_HIGH_LEVEL_OF_NESTING_FOR_SELECT';
codes[1474] = 'ER_NAME_BECOMES_EMPTY';
codes[1475] = 'ER_AMBIGUOUS_FIELD_TERM';
codes[1476] = 'ER_FOREIGN_SERVER_EXISTS';
codes[1477] = 'ER_FOREIGN_SERVER_DOESNT_EXIST';
codes[1478] = 'ER_ILLEGAL_HA_CREATE_OPTION';
codes[1479] = 'ER_PARTITION_REQUIRES_VALUES_ERROR';
codes[1480] = 'ER_PARTITION_WRONG_VALUES_ERROR';
codes[1481] = 'ER_PARTITION_MAXVALUE_ERROR';
codes[1482] = 'ER_PARTITION_SUBPARTITION_ERROR';
codes[1483] = 'ER_PARTITION_SUBPART_MIX_ERROR';
codes[1484] = 'ER_PARTITION_WRONG_NO_PART_ERROR';
codes[1485] = 'ER_PARTITION_WRONG_NO_SUBPART_ERROR';
codes[1486] = 'ER_WRONG_EXPR_IN_PARTITION_FUNC_ERROR';
codes[1487] = 'ER_NOT_CONSTANT_EXPRESSION';
codes[1488] = 'ER_FIELD_NOT_FOUND_PART_ERROR';
codes[1489] = 'ER_LIST_OF_FIELDS_ONLY_IN_HASH_ERROR';
codes[1490] = 'ER_INCONSISTENT_PARTITION_INFO_ERROR';
codes[1491] = 'ER_PARTITION_FUNC_NOT_ALLOWED_ERROR';
codes[1492] = 'ER_PARTITIONS_MUST_BE_DEFINED_ERROR';
codes[1493] = 'ER_RANGE_NOT_INCREASING_ERROR';
codes[1494] = 'ER_INCONSISTENT_TYPE_OF_FUNCTIONS_ERROR';
codes[1495] = 'ER_MULTIPLE_DEF_CONST_IN_LIST_PART_ERROR';
codes[1496] = 'ER_PARTITION_ENTRY_ERROR';
codes[1497] = 'ER_MIX_HANDLER_ERROR';
codes[1498] = 'ER_PARTITION_NOT_DEFINED_ERROR';
codes[1499] = 'ER_TOO_MANY_PARTITIONS_ERROR';
codes[1500] = 'ER_SUBPARTITION_ERROR';
codes[1501] = 'ER_CANT_CREATE_HANDLER_FILE';
codes[1502] = 'ER_BLOB_FIELD_IN_PART_FUNC_ERROR';
codes[1503] = 'ER_UNIQUE_KEY_NEED_ALL_FIELDS_IN_PF';
codes[1504] = 'ER_NO_PARTS_ERROR';
codes[1505] = 'ER_PARTITION_MGMT_ON_NONPARTITIONED';
codes[1506] = 'ER_FEATURE_NOT_SUPPORTED_WITH_PARTITIONING';
codes[1507] = 'ER_PARTITION_DOES_NOT_EXIST';
codes[1508] = 'ER_DROP_LAST_PARTITION';
codes[1509] = 'ER_COALESCE_ONLY_ON_HASH_PARTITION';
codes[1510] = 'ER_REORG_HASH_ONLY_ON_SAME_NO';
codes[1511] = 'ER_REORG_NO_PARAM_ERROR';
codes[1512] = 'ER_ONLY_ON_RANGE_LIST_PARTITION';
codes[1513] = 'ER_ADD_PARTITION_SUBPART_ERROR';
codes[1514] = 'ER_ADD_PARTITION_NO_NEW_PARTITION';
codes[1515] = 'ER_COALESCE_PARTITION_NO_PARTITION';
codes[1516] = 'ER_REORG_PARTITION_NOT_EXIST';
codes[1517] = 'ER_SAME_NAME_PARTITION';
codes[1518] = 'ER_NO_BINLOG_ERROR';
codes[1519] = 'ER_CONSECUTIVE_REORG_PARTITIONS';
codes[1520] = 'ER_REORG_OUTSIDE_RANGE';
codes[1521] = 'ER_PARTITION_FUNCTION_FAILURE';
codes[1522] = 'ER_PART_STATE_ERROR';
codes[1523] = 'ER_LIMITED_PART_RANGE';
codes[1524] = 'ER_PLUGIN_IS_NOT_LOADED';
codes[1525] = 'ER_WRONG_VALUE';
codes[1526] = 'ER_NO_PARTITION_FOR_GIVEN_VALUE';
codes[1527] = 'ER_FILEGROUP_OPTION_ONLY_ONCE';
codes[1528] = 'ER_CREATE_FILEGROUP_FAILED';
codes[1529] = 'ER_DROP_FILEGROUP_FAILED';
codes[1530] = 'ER_TABLESPACE_AUTO_EXTEND_ERROR';
codes[1531] = 'ER_WRONG_SIZE_NUMBER';
codes[1532] = 'ER_SIZE_OVERFLOW_ERROR';
codes[1533] = 'ER_ALTER_FILEGROUP_FAILED';
codes[1534] = 'ER_BINLOG_ROW_LOGGING_FAILED';
codes[1535] = 'ER_BINLOG_ROW_WRONG_TABLE_DEF';
codes[1536] = 'ER_BINLOG_ROW_RBR_TO_SBR';
codes[1537] = 'ER_EVENT_ALREADY_EXISTS';
codes[1538] = 'ER_EVENT_STORE_FAILED';
codes[1539] = 'ER_EVENT_DOES_NOT_EXIST';
codes[1540] = 'ER_EVENT_CANT_ALTER';
codes[1541] = 'ER_EVENT_DROP_FAILED';
codes[1542] = 'ER_EVENT_INTERVAL_NOT_POSITIVE_OR_TOO_BIG';
codes[1543] = 'ER_EVENT_ENDS_BEFORE_STARTS';
codes[1544] = 'ER_EVENT_EXEC_TIME_IN_THE_PAST';
codes[1545] = 'ER_EVENT_OPEN_TABLE_FAILED';
codes[1546] = 'ER_EVENT_NEITHER_M_EXPR_NOR_M_AT';
codes[1547] = 'ER_UNUSED_2';
codes[1548] = 'ER_UNUSED_3';
codes[1549] = 'ER_EVENT_CANNOT_DELETE';
codes[1550] = 'ER_EVENT_COMPILE_ERROR';
codes[1551] = 'ER_EVENT_SAME_NAME';
codes[1552] = 'ER_EVENT_DATA_TOO_LONG';
codes[1553] = 'ER_DROP_INDEX_FK';
codes[1554] = 'ER_WARN_DEPRECATED_SYNTAX_WITH_VER';
codes[1555] = 'ER_CANT_WRITE_LOCK_LOG_TABLE';
codes[1556] = 'ER_CANT_LOCK_LOG_TABLE';
codes[1557] = 'ER_UNUSED_4';
codes[1558] = 'ER_COL_COUNT_DOESNT_MATCH_PLEASE_UPDATE';
codes[1559] = 'ER_TEMP_TABLE_PREVENTS_SWITCH_OUT_OF_RBR';
codes[1560] = 'ER_STORED_FUNCTION_PREVENTS_SWITCH_BINLOG_FORMAT';
codes[1561] = 'ER_UNUSED_13';
codes[1562] = 'ER_PARTITION_NO_TEMPORARY';
codes[1563] = 'ER_PARTITION_CONST_DOMAIN_ERROR';
codes[1564] = 'ER_PARTITION_FUNCTION_IS_NOT_ALLOWED';
codes[1565] = 'ER_DDL_LOG_ERROR';
codes[1566] = 'ER_NULL_IN_VALUES_LESS_THAN';
codes[1567] = 'ER_WRONG_PARTITION_NAME';
codes[1568] = 'ER_CANT_CHANGE_TX_CHARACTERISTICS';
codes[1569] = 'ER_DUP_ENTRY_AUTOINCREMENT_CASE';
codes[1570] = 'ER_EVENT_MODIFY_QUEUE_ERROR';
codes[1571] = 'ER_EVENT_SET_VAR_ERROR';
codes[1572] = 'ER_PARTITION_MERGE_ERROR';
codes[1573] = 'ER_CANT_ACTIVATE_LOG';
codes[1574] = 'ER_RBR_NOT_AVAILABLE';
codes[1575] = 'ER_BASE64_DECODE_ERROR';
codes[1576] = 'ER_EVENT_RECURSION_FORBIDDEN';
codes[1577] = 'ER_EVENTS_DB_ERROR';
codes[1578] = 'ER_ONLY_INTEGERS_ALLOWED';
codes[1579] = 'ER_UNSUPORTED_LOG_ENGINE';
codes[1580] = 'ER_BAD_LOG_STATEMENT';
codes[1581] = 'ER_CANT_RENAME_LOG_TABLE';
codes[1582] = 'ER_WRONG_PARAMCOUNT_TO_NATIVE_FCT';
codes[1583] = 'ER_WRONG_PARAMETERS_TO_NATIVE_FCT';
codes[1584] = 'ER_WRONG_PARAMETERS_TO_STORED_FCT';
codes[1585] = 'ER_NATIVE_FCT_NAME_COLLISION';
codes[1586] = 'ER_DUP_ENTRY_WITH_KEY_NAME';
codes[1587] = 'ER_BINLOG_PURGE_EMFILE';
codes[1588] = 'ER_EVENT_CANNOT_CREATE_IN_THE_PAST';
codes[1589] = 'ER_EVENT_CANNOT_ALTER_IN_THE_PAST';
codes[1590] = 'ER_SLAVE_INCIDENT';
codes[1591] = 'ER_NO_PARTITION_FOR_GIVEN_VALUE_SILENT';
codes[1592] = 'ER_BINLOG_UNSAFE_STATEMENT';
codes[1593] = 'ER_SLAVE_FATAL_ERROR';
codes[1594] = 'ER_SLAVE_RELAY_LOG_READ_FAILURE';
codes[1595] = 'ER_SLAVE_RELAY_LOG_WRITE_FAILURE';
codes[1596] = 'ER_SLAVE_CREATE_EVENT_FAILURE';
codes[1597] = 'ER_SLAVE_MASTER_COM_FAILURE';
codes[1598] = 'ER_BINLOG_LOGGING_IMPOSSIBLE';
codes[1599] = 'ER_VIEW_NO_CREATION_CTX';
codes[1600] = 'ER_VIEW_INVALID_CREATION_CTX';
codes[1601] = 'ER_SR_INVALID_CREATION_CTX';
codes[1602] = 'ER_TRG_CORRUPTED_FILE';
codes[1603] = 'ER_TRG_NO_CREATION_CTX';
codes[1604] = 'ER_TRG_INVALID_CREATION_CTX';
codes[1605] = 'ER_EVENT_INVALID_CREATION_CTX';
codes[1606] = 'ER_TRG_CANT_OPEN_TABLE';
codes[1607] = 'ER_CANT_CREATE_SROUTINE';
codes[1608] = 'ER_UNUSED_11';
codes[1609] = 'ER_NO_FORMAT_DESCRIPTION_EVENT_BEFORE_BINLOG_STATEMENT';
codes[1610] = 'ER_SLAVE_CORRUPT_EVENT';
codes[1611] = 'ER_LOAD_DATA_INVALID_COLUMN';
codes[1612] = 'ER_LOG_PURGE_NO_FILE';
codes[1613] = 'ER_XA_RBTIMEOUT';
codes[1614] = 'ER_XA_RBDEADLOCK';
codes[1615] = 'ER_NEED_REPREPARE';
codes[1616] = 'ER_DELAYED_NOT_SUPPORTED';
codes[1617] = 'WARN_NO_MASTER_INFO';
codes[1618] = 'WARN_OPTION_IGNORED';
codes[1619] = 'ER_PLUGIN_DELETE_BUILTIN';
codes[1620] = 'WARN_PLUGIN_BUSY';
codes[1621] = 'ER_VARIABLE_IS_READONLY';
codes[1622] = 'ER_WARN_ENGINE_TRANSACTION_ROLLBACK';
codes[1623] = 'ER_SLAVE_HEARTBEAT_FAILURE';
codes[1624] = 'ER_SLAVE_HEARTBEAT_VALUE_OUT_OF_RANGE';
codes[1625] = 'ER_UNUSED_14';
codes[1626] = 'ER_CONFLICT_FN_PARSE_ERROR';
codes[1627] = 'ER_EXCEPTIONS_WRITE_ERROR';
codes[1628] = 'ER_TOO_LONG_TABLE_COMMENT';
codes[1629] = 'ER_TOO_LONG_FIELD_COMMENT';
codes[1630] = 'ER_FUNC_INEXISTENT_NAME_COLLISION';
codes[1631] = 'ER_DATABASE_NAME';
codes[1632] = 'ER_TABLE_NAME';
codes[1633] = 'ER_PARTITION_NAME';
codes[1634] = 'ER_SUBPARTITION_NAME';
codes[1635] = 'ER_TEMPORARY_NAME';
codes[1636] = 'ER_RENAMED_NAME';
codes[1637] = 'ER_TOO_MANY_CONCURRENT_TRXS';
codes[1638] = 'WARN_NON_ASCII_SEPARATOR_NOT_IMPLEMENTED';
codes[1639] = 'ER_DEBUG_SYNC_TIMEOUT';
codes[1640] = 'ER_DEBUG_SYNC_HIT_LIMIT';
codes[1641] = 'ER_DUP_SIGNAL_SET';
codes[1642] = 'ER_SIGNAL_WARN';
codes[1643] = 'ER_SIGNAL_NOT_FOUND';
codes[1644] = 'ER_SIGNAL_EXCEPTION';
codes[1645] = 'ER_RESIGNAL_WITHOUT_ACTIVE_HANDLER';
codes[1646] = 'ER_SIGNAL_BAD_CONDITION_TYPE';
codes[1647] = 'WARN_COND_ITEM_TRUNCATED';
codes[1648] = 'ER_COND_ITEM_TOO_LONG';
codes[1649] = 'ER_UNKNOWN_LOCALE';
codes[1650] = 'ER_SLAVE_IGNORE_SERVER_IDS';
codes[1651] = 'ER_QUERY_CACHE_DISABLED';
codes[1652] = 'ER_SAME_NAME_PARTITION_FIELD';
codes[1653] = 'ER_PARTITION_COLUMN_LIST_ERROR';
codes[1654] = 'ER_WRONG_TYPE_COLUMN_VALUE_ERROR';
codes[1655] = 'ER_TOO_MANY_PARTITION_FUNC_FIELDS_ERROR';
codes[1656] = 'ER_MAXVALUE_IN_VALUES_IN';
codes[1657] = 'ER_TOO_MANY_VALUES_ERROR';
codes[1658] = 'ER_ROW_SINGLE_PARTITION_FIELD_ERROR';
codes[1659] = 'ER_FIELD_TYPE_NOT_ALLOWED_AS_PARTITION_FIELD';
codes[1660] = 'ER_PARTITION_FIELDS_TOO_LONG';
codes[1661] = 'ER_BINLOG_ROW_ENGINE_AND_STMT_ENGINE';
codes[1662] = 'ER_BINLOG_ROW_MODE_AND_STMT_ENGINE';
codes[1663] = 'ER_BINLOG_UNSAFE_AND_STMT_ENGINE';
codes[1664] = 'ER_BINLOG_ROW_INJECTION_AND_STMT_ENGINE';
codes[1665] = 'ER_BINLOG_STMT_MODE_AND_ROW_ENGINE';
codes[1666] = 'ER_BINLOG_ROW_INJECTION_AND_STMT_MODE';
codes[1667] = 'ER_BINLOG_MULTIPLE_ENGINES_AND_SELF_LOGGING_ENGINE';
codes[1668] = 'ER_BINLOG_UNSAFE_LIMIT';
codes[1669] = 'ER_BINLOG_UNSAFE_INSERT_DELAYED';
codes[1670] = 'ER_BINLOG_UNSAFE_SYSTEM_TABLE';
codes[1671] = 'ER_BINLOG_UNSAFE_AUTOINC_COLUMNS';
codes[1672] = 'ER_BINLOG_UNSAFE_UDF';
codes[1673] = 'ER_BINLOG_UNSAFE_SYSTEM_VARIABLE';
codes[1674] = 'ER_BINLOG_UNSAFE_SYSTEM_FUNCTION';
codes[1675] = 'ER_BINLOG_UNSAFE_NONTRANS_AFTER_TRANS';
codes[1676] = 'ER_MESSAGE_AND_STATEMENT';
codes[1677] = 'ER_SLAVE_CONVERSION_FAILED';
codes[1678] = 'ER_SLAVE_CANT_CREATE_CONVERSION';
codes[1679] = 'ER_INSIDE_TRANSACTION_PREVENTS_SWITCH_BINLOG_FORMAT';
codes[1680] = 'ER_PATH_LENGTH';
codes[1681] = 'ER_WARN_DEPRECATED_SYNTAX_NO_REPLACEMENT';
codes[1682] = 'ER_WRONG_NATIVE_TABLE_STRUCTURE';
codes[1683] = 'ER_WRONG_PERFSCHEMA_USAGE';
codes[1684] = 'ER_WARN_I_S_SKIPPED_TABLE';
codes[1685] = 'ER_INSIDE_TRANSACTION_PREVENTS_SWITCH_BINLOG_DIRECT';
codes[1686] = 'ER_STORED_FUNCTION_PREVENTS_SWITCH_BINLOG_DIRECT';
codes[1687] = 'ER_SPATIAL_MUST_HAVE_GEOM_COL';
codes[1688] = 'ER_TOO_LONG_INDEX_COMMENT';
codes[1689] = 'ER_LOCK_ABORTED';
codes[1690] = 'ER_DATA_OUT_OF_RANGE';
codes[1691] = 'ER_WRONG_SPVAR_TYPE_IN_LIMIT';
codes[1692] = 'ER_BINLOG_UNSAFE_MULTIPLE_ENGINES_AND_SELF_LOGGING_ENGINE';
codes[1693] = 'ER_BINLOG_UNSAFE_MIXED_STATEMENT';
codes[1694] = 'ER_INSIDE_TRANSACTION_PREVENTS_SWITCH_SQL_LOG_BIN';
codes[1695] = 'ER_STORED_FUNCTION_PREVENTS_SWITCH_SQL_LOG_BIN';
codes[1696] = 'ER_FAILED_READ_FROM_PAR_FILE';
codes[1697] = 'ER_VALUES_IS_NOT_INT_TYPE_ERROR';
codes[1698] = 'ER_ACCESS_DENIED_NO_PASSWORD_ERROR';
codes[1699] = 'ER_SET_PASSWORD_AUTH_PLUGIN';
codes[1700] = 'ER_GRANT_PLUGIN_USER_EXISTS';
codes[1701] = 'ER_TRUNCATE_ILLEGAL_FK';
codes[1702] = 'ER_PLUGIN_IS_PERMANENT';
codes[1703] = 'ER_SLAVE_HEARTBEAT_VALUE_OUT_OF_RANGE_MIN';
codes[1704] = 'ER_SLAVE_HEARTBEAT_VALUE_OUT_OF_RANGE_MAX';
codes[1705] = 'ER_STMT_CACHE_FULL';
codes[1706] = 'ER_MULTI_UPDATE_KEY_CONFLICT';
codes[1707] = 'ER_TABLE_NEEDS_REBUILD';
codes[1708] = 'WARN_OPTION_BELOW_LIMIT';
codes[1709] = 'ER_INDEX_COLUMN_TOO_LONG';
codes[1710] = 'ER_ERROR_IN_TRIGGER_BODY';
codes[1711] = 'ER_ERROR_IN_UNKNOWN_TRIGGER_BODY';
codes[1712] = 'ER_INDEX_CORRUPT';
codes[1713] = 'ER_UNDO_RECORD_TOO_BIG';
codes[1714] = 'ER_BINLOG_UNSAFE_INSERT_IGNORE_SELECT';
codes[1715] = 'ER_BINLOG_UNSAFE_INSERT_SELECT_UPDATE';
codes[1716] = 'ER_BINLOG_UNSAFE_REPLACE_SELECT';
codes[1717] = 'ER_BINLOG_UNSAFE_CREATE_IGNORE_SELECT';
codes[1718] = 'ER_BINLOG_UNSAFE_CREATE_REPLACE_SELECT';
codes[1719] = 'ER_BINLOG_UNSAFE_UPDATE_IGNORE';
codes[1720] = 'ER_UNUSED_15';
codes[1721] = 'ER_UNUSED_16';
codes[1722] = 'ER_BINLOG_UNSAFE_WRITE_AUTOINC_SELECT';
codes[1723] = 'ER_BINLOG_UNSAFE_CREATE_SELECT_AUTOINC';
codes[1724] = 'ER_BINLOG_UNSAFE_INSERT_TWO_KEYS';
codes[1725] = 'ER_UNUSED_28';
codes[1726] = 'ER_VERS_NOT_ALLOWED';
codes[1727] = 'ER_BINLOG_UNSAFE_AUTOINC_NOT_FIRST';
codes[1728] = 'ER_CANNOT_LOAD_FROM_TABLE_V2';
codes[1729] = 'ER_MASTER_DELAY_VALUE_OUT_OF_RANGE';
codes[1730] = 'ER_ONLY_FD_AND_RBR_EVENTS_ALLOWED_IN_BINLOG_STATEMENT';
codes[1731] = 'ER_PARTITION_EXCHANGE_DIFFERENT_OPTION';
codes[1732] = 'ER_PARTITION_EXCHANGE_PART_TABLE';
codes[1733] = 'ER_PARTITION_EXCHANGE_TEMP_TABLE';
codes[1734] = 'ER_PARTITION_INSTEAD_OF_SUBPARTITION';
codes[1735] = 'ER_UNKNOWN_PARTITION';
codes[1736] = 'ER_TABLES_DIFFERENT_METADATA';
codes[1737] = 'ER_ROW_DOES_NOT_MATCH_PARTITION';
codes[1738] = 'ER_BINLOG_CACHE_SIZE_GREATER_THAN_MAX';
codes[1739] = 'ER_WARN_INDEX_NOT_APPLICABLE';
codes[1740] = 'ER_PARTITION_EXCHANGE_FOREIGN_KEY';
codes[1741] = 'ER_NO_SUCH_KEY_VALUE';
codes[1742] = 'ER_VALUE_TOO_LONG';
codes[1743] = 'ER_NETWORK_READ_EVENT_CHECKSUM_FAILURE';
codes[1744] = 'ER_BINLOG_READ_EVENT_CHECKSUM_FAILURE';
codes[1745] = 'ER_BINLOG_STMT_CACHE_SIZE_GREATER_THAN_MAX';
codes[1746] = 'ER_CANT_UPDATE_TABLE_IN_CREATE_TABLE_SELECT';
codes[1747] = 'ER_PARTITION_CLAUSE_ON_NONPARTITIONED';
codes[1748] = 'ER_ROW_DOES_NOT_MATCH_GIVEN_PARTITION_SET';
codes[1749] = 'ER_UNUSED_5';
codes[1750] = 'ER_CHANGE_RPL_INFO_REPOSITORY_FAILURE';
codes[1751] = 'ER_WARNING_NOT_COMPLETE_ROLLBACK_WITH_CREATED_TEMP_TABLE';
codes[1752] = 'ER_WARNING_NOT_COMPLETE_ROLLBACK_WITH_DROPPED_TEMP_TABLE';
codes[1753] = 'ER_MTS_FEATURE_IS_NOT_SUPPORTED';
codes[1754] = 'ER_MTS_UPDATED_DBS_GREATER_MAX';
codes[1755] = 'ER_MTS_CANT_PARALLEL';
codes[1756] = 'ER_MTS_INCONSISTENT_DATA';
codes[1757] = 'ER_FULLTEXT_NOT_SUPPORTED_WITH_PARTITIONING';
codes[1758] = 'ER_DA_INVALID_CONDITION_NUMBER';
codes[1759] = 'ER_INSECURE_PLAIN_TEXT';
codes[1760] = 'ER_INSECURE_CHANGE_MASTER';
codes[1761] = 'ER_FOREIGN_DUPLICATE_KEY_WITH_CHILD_INFO';
codes[1762] = 'ER_FOREIGN_DUPLICATE_KEY_WITHOUT_CHILD_INFO';
codes[1763] = 'ER_SQLTHREAD_WITH_SECURE_SLAVE';
codes[1764] = 'ER_TABLE_HAS_NO_FT';
codes[1765] = 'ER_VARIABLE_NOT_SETTABLE_IN_SF_OR_TRIGGER';
codes[1766] = 'ER_VARIABLE_NOT_SETTABLE_IN_TRANSACTION';
codes[1767] = 'ER_GTID_NEXT_IS_NOT_IN_GTID_NEXT_LIST';
codes[1768] = 'ER_CANT_CHANGE_GTID_NEXT_IN_TRANSACTION_WHEN_GTID_NEXT_LIST_IS_NULL';
codes[1769] = 'ER_SET_STATEMENT_CANNOT_INVOKE_FUNCTION';
codes[1770] = 'ER_GTID_NEXT_CANT_BE_AUTOMATIC_IF_GTID_NEXT_LIST_IS_NON_NULL';
codes[1771] = 'ER_SKIPPING_LOGGED_TRANSACTION';
codes[1772] = 'ER_MALFORMED_GTID_SET_SPECIFICATION';
codes[1773] = 'ER_MALFORMED_GTID_SET_ENCODING';
codes[1774] = 'ER_MALFORMED_GTID_SPECIFICATION';
codes[1775] = 'ER_GNO_EXHAUSTED';
codes[1776] = 'ER_BAD_SLAVE_AUTO_POSITION';
codes[1777] = 'ER_AUTO_POSITION_REQUIRES_GTID_MODE_ON';
codes[1778] = 'ER_CANT_DO_IMPLICIT_COMMIT_IN_TRX_WHEN_GTID_NEXT_IS_SET';
codes[1779] = 'ER_GTID_MODE_2_OR_3_REQUIRES_ENFORCE_GTID_CONSISTENCY_ON';
codes[1780] = 'ER_GTID_MODE_REQUIRES_BINLOG';
codes[1781] = 'ER_CANT_SET_GTID_NEXT_TO_GTID_WHEN_GTID_MODE_IS_OFF';
codes[1782] = 'ER_CANT_SET_GTID_NEXT_TO_ANONYMOUS_WHEN_GTID_MODE_IS_ON';
codes[1783] = 'ER_CANT_SET_GTID_NEXT_LIST_TO_NON_NULL_WHEN_GTID_MODE_IS_OFF';
codes[1784] = 'ER_FOUND_GTID_EVENT_WHEN_GTID_MODE_IS_OFF';
codes[1785] = 'ER_GTID_UNSAFE_NON_TRANSACTIONAL_TABLE';
codes[1786] = 'ER_GTID_UNSAFE_CREATE_SELECT';
codes[1787] = 'ER_GTID_UNSAFE_CREATE_DROP_TEMPORARY_TABLE_IN_TRANSACTION';
codes[1788] = 'ER_GTID_MODE_CAN_ONLY_CHANGE_ONE_STEP_AT_A_TIME';
codes[1789] = 'ER_MASTER_HAS_PURGED_REQUIRED_GTIDS';
codes[1790] = 'ER_CANT_SET_GTID_NEXT_WHEN_OWNING_GTID';
codes[1791] = 'ER_UNKNOWN_EXPLAIN_FORMAT';
codes[1792] = 'ER_CANT_EXECUTE_IN_READ_ONLY_TRANSACTION';
codes[1793] = 'ER_TOO_LONG_TABLE_PARTITION_COMMENT';
codes[1794] = 'ER_SLAVE_CONFIGURATION';
codes[1795] = 'ER_INNODB_FT_LIMIT';
codes[1796] = 'ER_INNODB_NO_FT_TEMP_TABLE';
codes[1797] = 'ER_INNODB_FT_WRONG_DOCID_COLUMN';
codes[1798] = 'ER_INNODB_FT_WRONG_DOCID_INDEX';
codes[1799] = 'ER_INNODB_ONLINE_LOG_TOO_BIG';
codes[1800] = 'ER_UNKNOWN_ALTER_ALGORITHM';
codes[1801] = 'ER_UNKNOWN_ALTER_LOCK';
codes[1802] = 'ER_MTS_CHANGE_MASTER_CANT_RUN_WITH_GAPS';
codes[1803] = 'ER_MTS_RECOVERY_FAILURE';
codes[1804] = 'ER_MTS_RESET_WORKERS';
codes[1805] = 'ER_COL_COUNT_DOESNT_MATCH_CORRUPTED_V2';
codes[1806] = 'ER_SLAVE_SILENT_RETRY_TRANSACTION';
codes[1807] = 'ER_UNUSED_22';
codes[1808] = 'ER_TABLE_SCHEMA_MISMATCH';
codes[1809] = 'ER_TABLE_IN_SYSTEM_TABLESPACE';
codes[1810] = 'ER_IO_READ_ERROR';
codes[1811] = 'ER_IO_WRITE_ERROR';
codes[1812] = 'ER_TABLESPACE_MISSING';
codes[1813] = 'ER_TABLESPACE_EXISTS';
codes[1814] = 'ER_TABLESPACE_DISCARDED';
codes[1815] = 'ER_INTERNAL_ERROR';
codes[1816] = 'ER_INNODB_IMPORT_ERROR';
codes[1817] = 'ER_INNODB_INDEX_CORRUPT';
codes[1818] = 'ER_INVALID_YEAR_COLUMN_LENGTH';
codes[1819] = 'ER_NOT_VALID_PASSWORD';
codes[1820] = 'ER_MUST_CHANGE_PASSWORD';
codes[1821] = 'ER_FK_NO_INDEX_CHILD';
codes[1822] = 'ER_FK_NO_INDEX_PARENT';
codes[1823] = 'ER_FK_FAIL_ADD_SYSTEM';
codes[1824] = 'ER_FK_CANNOT_OPEN_PARENT';
codes[1825] = 'ER_FK_INCORRECT_OPTION';
codes[1826] = 'ER_DUP_CONSTRAINT_NAME';
codes[1827] = 'ER_PASSWORD_FORMAT';
codes[1828] = 'ER_FK_COLUMN_CANNOT_DROP';
codes[1829] = 'ER_FK_COLUMN_CANNOT_DROP_CHILD';
codes[1830] = 'ER_FK_COLUMN_NOT_NULL';
codes[1831] = 'ER_DUP_INDEX';
codes[1832] = 'ER_FK_COLUMN_CANNOT_CHANGE';
codes[1833] = 'ER_FK_COLUMN_CANNOT_CHANGE_CHILD';
codes[1834] = 'ER_FK_CANNOT_DELETE_PARENT';
codes[1835] = 'ER_MALFORMED_PACKET';
codes[1836] = 'ER_READ_ONLY_MODE';
codes[1837] = 'ER_GTID_NEXT_TYPE_UNDEFINED_GROUP';
codes[1838] = 'ER_VARIABLE_NOT_SETTABLE_IN_SP';
codes[1839] = 'ER_CANT_SET_GTID_PURGED_WHEN_GTID_MODE_IS_OFF';
codes[1840] = 'ER_CANT_SET_GTID_PURGED_WHEN_GTID_EXECUTED_IS_NOT_EMPTY';
codes[1841] = 'ER_CANT_SET_GTID_PURGED_WHEN_OWNED_GTIDS_IS_NOT_EMPTY';
codes[1842] = 'ER_GTID_PURGED_WAS_CHANGED';
codes[1843] = 'ER_GTID_EXECUTED_WAS_CHANGED';
codes[1844] = 'ER_BINLOG_STMT_MODE_AND_NO_REPL_TABLES';
codes[1845] = 'ER_ALTER_OPERATION_NOT_SUPPORTED';
codes[1846] = 'ER_ALTER_OPERATION_NOT_SUPPORTED_REASON';
codes[1847] = 'ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_COPY';
codes[1848] = 'ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_PARTITION';
codes[1849] = 'ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_FK_RENAME';
codes[1850] = 'ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_COLUMN_TYPE';
codes[1851] = 'ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_FK_CHECK';
codes[1852] = 'ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_IGNORE';
codes[1853] = 'ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_NOPK';
codes[1854] = 'ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_AUTOINC';
codes[1855] = 'ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_HIDDEN_FTS';
codes[1856] = 'ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_CHANGE_FTS';
codes[1857] = 'ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_FTS';
codes[1858] = 'ER_SQL_SLAVE_SKIP_COUNTER_NOT_SETTABLE_IN_GTID_MODE';
codes[1859] = 'ER_DUP_UNKNOWN_IN_INDEX';
codes[1860] = 'ER_IDENT_CAUSES_TOO_LONG_PATH';
codes[1861] = 'ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_NOT_NULL';
codes[1862] = 'ER_MUST_CHANGE_PASSWORD_LOGIN';
codes[1863] = 'ER_ROW_IN_WRONG_PARTITION';
codes[1864] = 'ER_MTS_EVENT_BIGGER_PENDING_JOBS_SIZE_MAX';
codes[1865] = 'ER_INNODB_NO_FT_USES_PARSER';
codes[1866] = 'ER_BINLOG_LOGICAL_CORRUPTION';
codes[1867] = 'ER_WARN_PURGE_LOG_IN_USE';
codes[1868] = 'ER_WARN_PURGE_LOG_IS_ACTIVE';
codes[1869] = 'ER_AUTO_INCREMENT_CONFLICT';
codes[1870] = 'WARN_ON_BLOCKHOLE_IN_RBR';
codes[1871] = 'ER_SLAVE_MI_INIT_REPOSITORY';
codes[1872] = 'ER_SLAVE_RLI_INIT_REPOSITORY';
codes[1873] = 'ER_ACCESS_DENIED_CHANGE_USER_ERROR';
codes[1874] = 'ER_INNODB_READ_ONLY';
codes[1875] = 'ER_STOP_SLAVE_SQL_THREAD_TIMEOUT';
codes[1876] = 'ER_STOP_SLAVE_IO_THREAD_TIMEOUT';
codes[1877] = 'ER_TABLE_CORRUPT';
codes[1878] = 'ER_TEMP_FILE_WRITE_FAILURE';
codes[1879] = 'ER_INNODB_FT_AUX_NOT_HEX_ID';
codes[1880] = 'ER_LAST_MYSQL_ERROR_MESSAGE';
codes[1900] = 'ER_UNUSED_18';
codes[1901] = 'ER_GENERATED_COLUMN_FUNCTION_IS_NOT_ALLOWED';
codes[1902] = 'ER_UNUSED_19';
codes[1903] = 'ER_PRIMARY_KEY_BASED_ON_GENERATED_COLUMN';
codes[1904] = 'ER_KEY_BASED_ON_GENERATED_VIRTUAL_COLUMN';
codes[1905] = 'ER_WRONG_FK_OPTION_FOR_GENERATED_COLUMN';
codes[1906] = 'ER_WARNING_NON_DEFAULT_VALUE_FOR_GENERATED_COLUMN';
codes[1907] = 'ER_UNSUPPORTED_ACTION_ON_GENERATED_COLUMN';
codes[1908] = 'ER_UNUSED_20';
codes[1909] = 'ER_UNUSED_21';
codes[1910] = 'ER_UNSUPPORTED_ENGINE_FOR_GENERATED_COLUMNS';
codes[1911] = 'ER_UNKNOWN_OPTION';
codes[1912] = 'ER_BAD_OPTION_VALUE';
codes[1913] = 'ER_UNUSED_6';
codes[1914] = 'ER_UNUSED_7';
codes[1915] = 'ER_UNUSED_8';
codes[1916] = 'ER_DATA_OVERFLOW';
codes[1917] = 'ER_DATA_TRUNCATED';
codes[1918] = 'ER_BAD_DATA';
codes[1919] = 'ER_DYN_COL_WRONG_FORMAT';
codes[1920] = 'ER_DYN_COL_IMPLEMENTATION_LIMIT';
codes[1921] = 'ER_DYN_COL_DATA';
codes[1922] = 'ER_DYN_COL_WRONG_CHARSET';
codes[1923] = 'ER_ILLEGAL_SUBQUERY_OPTIMIZER_SWITCHES';
codes[1924] = 'ER_QUERY_CACHE_IS_DISABLED';
codes[1925] = 'ER_QUERY_CACHE_IS_GLOBALY_DISABLED';
codes[1926] = 'ER_VIEW_ORDERBY_IGNORED';
codes[1927] = 'ER_CONNECTION_KILLED';
codes[1928] = 'ER_UNUSED_12';
codes[1929] = 'ER_INSIDE_TRANSACTION_PREVENTS_SWITCH_SKIP_REPLICATION';
codes[1930] = 'ER_STORED_FUNCTION_PREVENTS_SWITCH_SKIP_REPLICATION';
codes[1931] = 'ER_QUERY_EXCEEDED_ROWS_EXAMINED_LIMIT';
codes[1932] = 'ER_NO_SUCH_TABLE_IN_ENGINE';
codes[1933] = 'ER_TARGET_NOT_EXPLAINABLE';
codes[1934] = 'ER_CONNECTION_ALREADY_EXISTS';
codes[1935] = 'ER_MASTER_LOG_PREFIX';
codes[1936] = 'ER_CANT_START_STOP_SLAVE';
codes[1937] = 'ER_SLAVE_STARTED';
codes[1938] = 'ER_SLAVE_STOPPED';
codes[1939] = 'ER_SQL_DISCOVER_ERROR';
codes[1940] = 'ER_FAILED_GTID_STATE_INIT';
codes[1941] = 'ER_INCORRECT_GTID_STATE';
codes[1942] = 'ER_CANNOT_UPDATE_GTID_STATE';
codes[1943] = 'ER_DUPLICATE_GTID_DOMAIN';
codes[1944] = 'ER_GTID_OPEN_TABLE_FAILED';
codes[1945] = 'ER_GTID_POSITION_NOT_FOUND_IN_BINLOG';
codes[1946] = 'ER_CANNOT_LOAD_SLAVE_GTID_STATE';
codes[1947] = 'ER_MASTER_GTID_POS_CONFLICTS_WITH_BINLOG';
codes[1948] = 'ER_MASTER_GTID_POS_MISSING_DOMAIN';
codes[1949] = 'ER_UNTIL_REQUIRES_USING_GTID';
codes[1950] = 'ER_GTID_STRICT_OUT_OF_ORDER';
codes[1951] = 'ER_GTID_START_FROM_BINLOG_HOLE';
codes[1952] = 'ER_SLAVE_UNEXPECTED_MASTER_SWITCH';
codes[1953] = 'ER_INSIDE_TRANSACTION_PREVENTS_SWITCH_GTID_DOMAIN_ID_SEQ_NO';
codes[1954] = 'ER_STORED_FUNCTION_PREVENTS_SWITCH_GTID_DOMAIN_ID_SEQ_NO';
codes[1955] = 'ER_GTID_POSITION_NOT_FOUND_IN_BINLOG2';
codes[1956] = 'ER_BINLOG_MUST_BE_EMPTY';
codes[1957] = 'ER_NO_SUCH_QUERY';
codes[1958] = 'ER_BAD_BASE64_DATA';
codes[1959] = 'ER_INVALID_ROLE';
codes[1960] = 'ER_INVALID_CURRENT_USER';
codes[1961] = 'ER_CANNOT_GRANT_ROLE';
codes[1962] = 'ER_CANNOT_REVOKE_ROLE';
codes[1963] = 'ER_CHANGE_SLAVE_PARALLEL_THREADS_ACTIVE';
codes[1964] = 'ER_PRIOR_COMMIT_FAILED';
codes[1965] = 'ER_IT_IS_A_VIEW';
codes[1966] = 'ER_SLAVE_SKIP_NOT_IN_GTID';
codes[1967] = 'ER_TABLE_DEFINITION_TOO_BIG';
codes[1968] = 'ER_PLUGIN_INSTALLED';
codes[1969] = 'ER_STATEMENT_TIMEOUT';
codes[1970] = 'ER_SUBQUERIES_NOT_SUPPORTED';
codes[1971] = 'ER_SET_STATEMENT_NOT_SUPPORTED';
codes[1972] = 'ER_UNUSED_9';
codes[1973] = 'ER_USER_CREATE_EXISTS';
codes[1974] = 'ER_USER_DROP_EXISTS';
codes[1975] = 'ER_ROLE_CREATE_EXISTS';
codes[1976] = 'ER_ROLE_DROP_EXISTS';
codes[1977] = 'ER_CANNOT_CONVERT_CHARACTER';
codes[1978] = 'ER_INVALID_DEFAULT_VALUE_FOR_FIELD';
codes[1979] = 'ER_KILL_QUERY_DENIED_ERROR';
codes[1980] = 'ER_NO_EIS_FOR_FIELD';
codes[1981] = 'ER_WARN_AGGFUNC_DEPENDENCE';
codes[1982] = 'WARN_INNODB_PARTITION_OPTION_IGNORED';
codes[3000] = 'ER_FILE_CORRUPT';
codes[3001] = 'ER_ERROR_ON_MASTER';
codes[3002] = 'ER_INCONSISTENT_ERROR';
codes[3003] = 'ER_STORAGE_ENGINE_NOT_LOADED';
codes[3004] = 'ER_GET_STACKED_DA_WITHOUT_ACTIVE_HANDLER';
codes[3005] = 'ER_WARN_LEGACY_SYNTAX_CONVERTED';
codes[3006] = 'ER_BINLOG_UNSAFE_FULLTEXT_PLUGIN';
codes[3007] = 'ER_CANNOT_DISCARD_TEMPORARY_TABLE';
codes[3008] = 'ER_FK_DEPTH_EXCEEDED';
codes[3009] = 'ER_COL_COUNT_DOESNT_MATCH_PLEASE_UPDATE_V2';
codes[3010] = 'ER_WARN_TRIGGER_DOESNT_HAVE_CREATED';
codes[3011] = 'ER_REFERENCED_TRG_DOES_NOT_EXIST_MYSQL';
codes[3012] = 'ER_EXPLAIN_NOT_SUPPORTED';
codes[3013] = 'ER_INVALID_FIELD_SIZE';
codes[3014] = 'ER_MISSING_HA_CREATE_OPTION';
codes[3015] = 'ER_ENGINE_OUT_OF_MEMORY';
codes[3016] = 'ER_PASSWORD_EXPIRE_ANONYMOUS_USER';
codes[3017] = 'ER_SLAVE_SQL_THREAD_MUST_STOP';
codes[3018] = 'ER_NO_FT_MATERIALIZED_SUBQUERY';
codes[3019] = 'ER_INNODB_UNDO_LOG_FULL';
codes[3020] = 'ER_INVALID_ARGUMENT_FOR_LOGARITHM';
codes[3021] = 'ER_SLAVE_CHANNEL_IO_THREAD_MUST_STOP';
codes[3022] = 'ER_WARN_OPEN_TEMP_TABLES_MUST_BE_ZERO';
codes[3023] = 'ER_WARN_ONLY_MASTER_LOG_FILE_NO_POS';
codes[3024] = 'ER_QUERY_TIMEOUT';
codes[3025] = 'ER_NON_RO_SELECT_DISABLE_TIMER';
codes[3026] = 'ER_DUP_LIST_ENTRY';
codes[3027] = 'ER_SQL_MODE_NO_EFFECT';
codes[3028] = 'ER_AGGREGATE_ORDER_FOR_UNION';
codes[3029] = 'ER_AGGREGATE_ORDER_NON_AGG_QUERY';
codes[3030] = 'ER_SLAVE_WORKER_STOPPED_PREVIOUS_THD_ERROR';
codes[3031] = 'ER_DONT_SUPPORT_SLAVE_PRESERVE_COMMIT_ORDER';
codes[3032] = 'ER_SERVER_OFFLINE_MODE';
codes[3033] = 'ER_GIS_DIFFERENT_SRIDS';
codes[3034] = 'ER_GIS_UNSUPPORTED_ARGUMENT';
codes[3035] = 'ER_GIS_UNKNOWN_ERROR';
codes[3036] = 'ER_GIS_UNKNOWN_EXCEPTION';
codes[3037] = 'ER_GIS_INVALID_DATA';
codes[3038] = 'ER_BOOST_GEOMETRY_EMPTY_INPUT_EXCEPTION';
codes[3039] = 'ER_BOOST_GEOMETRY_CENTROID_EXCEPTION';
codes[3040] = 'ER_BOOST_GEOMETRY_OVERLAY_INVALID_INPUT_EXCEPTION';
codes[3041] = 'ER_BOOST_GEOMETRY_TURN_INFO_EXCEPTION';
codes[3042] = 'ER_BOOST_GEOMETRY_SELF_INTERSECTION_POINT_EXCEPTION';
codes[3043] = 'ER_BOOST_GEOMETRY_UNKNOWN_EXCEPTION';
codes[3044] = 'ER_STD_BAD_ALLOC_ERROR';
codes[3045] = 'ER_STD_DOMAIN_ERROR';
codes[3046] = 'ER_STD_LENGTH_ERROR';
codes[3047] = 'ER_STD_INVALID_ARGUMENT';
codes[3048] = 'ER_STD_OUT_OF_RANGE_ERROR';
codes[3049] = 'ER_STD_OVERFLOW_ERROR';
codes[3050] = 'ER_STD_RANGE_ERROR';
codes[3051] = 'ER_STD_UNDERFLOW_ERROR';
codes[3052] = 'ER_STD_LOGIC_ERROR';
codes[3053] = 'ER_STD_RUNTIME_ERROR';
codes[3054] = 'ER_STD_UNKNOWN_EXCEPTION';
codes[3055] = 'ER_GIS_DATA_WRONG_ENDIANESS';
codes[3056] = 'ER_CHANGE_MASTER_PASSWORD_LENGTH';
codes[3057] = 'ER_USER_LOCK_WRONG_NAME';
codes[3058] = 'ER_USER_LOCK_DEADLOCK';
codes[3059] = 'ER_REPLACE_INACCESSIBLE_ROWS';
codes[3060] = 'ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_GIS';
codes[4000] = 'ER_UNUSED_26';
codes[4001] = 'ER_UNUSED_27';
codes[4002] = 'ER_WITH_COL_WRONG_LIST';
codes[4003] = 'ER_TOO_MANY_DEFINITIONS_IN_WITH_CLAUSE';
codes[4004] = 'ER_DUP_QUERY_NAME';
codes[4005] = 'ER_RECURSIVE_WITHOUT_ANCHORS';
codes[4006] = 'ER_UNACCEPTABLE_MUTUAL_RECURSION';
codes[4007] = 'ER_REF_TO_RECURSIVE_WITH_TABLE_IN_DERIVED';
codes[4008] = 'ER_NOT_STANDARD_COMPLIANT_RECURSIVE';
codes[4009] = 'ER_WRONG_WINDOW_SPEC_NAME';
codes[4010] = 'ER_DUP_WINDOW_NAME';
codes[4011] = 'ER_PARTITION_LIST_IN_REFERENCING_WINDOW_SPEC';
codes[4012] = 'ER_ORDER_LIST_IN_REFERENCING_WINDOW_SPEC';
codes[4013] = 'ER_WINDOW_FRAME_IN_REFERENCED_WINDOW_SPEC';
codes[4014] = 'ER_BAD_COMBINATION_OF_WINDOW_FRAME_BOUND_SPECS';
codes[4015] = 'ER_WRONG_PLACEMENT_OF_WINDOW_FUNCTION';
codes[4016] = 'ER_WINDOW_FUNCTION_IN_WINDOW_SPEC';
codes[4017] = 'ER_NOT_ALLOWED_WINDOW_FRAME';
codes[4018] = 'ER_NO_ORDER_LIST_IN_WINDOW_SPEC';
codes[4019] = 'ER_RANGE_FRAME_NEEDS_SIMPLE_ORDERBY';
codes[4020] = 'ER_WRONG_TYPE_FOR_ROWS_FRAME';
codes[4021] = 'ER_WRONG_TYPE_FOR_RANGE_FRAME';
codes[4022] = 'ER_FRAME_EXCLUSION_NOT_SUPPORTED';
codes[4023] = 'ER_WINDOW_FUNCTION_DONT_HAVE_FRAME';
codes[4024] = 'ER_INVALID_NTILE_ARGUMENT';
codes[4025] = 'ER_CONSTRAINT_FAILED';
codes[4026] = 'ER_EXPRESSION_IS_TOO_BIG';
codes[4027] = 'ER_ERROR_EVALUATING_EXPRESSION';
codes[4028] = 'ER_CALCULATING_DEFAULT_VALUE';
codes[4029] = 'ER_EXPRESSION_REFERS_TO_UNINIT_FIELD';
codes[4030] = 'ER_PARTITION_DEFAULT_ERROR';
codes[4031] = 'ER_REFERENCED_TRG_DOES_NOT_EXIST';
codes[4032] = 'ER_INVALID_DEFAULT_PARAM';
codes[4033] = 'ER_BINLOG_NON_SUPPORTED_BULK';
codes[4034] = 'ER_BINLOG_UNCOMPRESS_ERROR';
codes[4035] = 'ER_JSON_BAD_CHR';
codes[4036] = 'ER_JSON_NOT_JSON_CHR';
codes[4037] = 'ER_JSON_EOS';
codes[4038] = 'ER_JSON_SYNTAX';
codes[4039] = 'ER_JSON_ESCAPING';
codes[4040] = 'ER_JSON_DEPTH';
codes[4041] = 'ER_JSON_PATH_EOS';
codes[4042] = 'ER_JSON_PATH_SYNTAX';
codes[4043] = 'ER_JSON_PATH_DEPTH';
codes[4044] = 'ER_JSON_PATH_NO_WILDCARD';
codes[4045] = 'ER_JSON_PATH_ARRAY';
codes[4046] = 'ER_JSON_ONE_OR_ALL';
codes[4047] = 'ER_UNSUPPORTED_COMPRESSED_TABLE';
codes[4048] = 'ER_GEOJSON_INCORRECT';
codes[4049] = 'ER_GEOJSON_TOO_FEW_POINTS';
codes[4050] = 'ER_GEOJSON_NOT_CLOSED';
codes[4051] = 'ER_JSON_PATH_EMPTY';
codes[4052] = 'ER_SLAVE_SAME_ID';
codes[4053] = 'ER_FLASHBACK_NOT_SUPPORTED';
codes[4054] = 'ER_KEYS_OUT_OF_ORDER';
codes[4055] = 'ER_OVERLAPPING_KEYS';
codes[4056] = 'ER_REQUIRE_ROW_BINLOG_FORMAT';
codes[4057] = 'ER_ISOLATION_MODE_NOT_SUPPORTED';
codes[4058] = 'ER_ON_DUPLICATE_DISABLED';
codes[4059] = 'ER_UPDATES_WITH_CONSISTENT_SNAPSHOT';
codes[4060] = 'ER_ROLLBACK_ONLY';
codes[4061] = 'ER_ROLLBACK_TO_SAVEPOINT';
codes[4062] = 'ER_ISOLATION_LEVEL_WITH_CONSISTENT_SNAPSHOT';
codes[4063] = 'ER_UNSUPPORTED_COLLATION';
codes[4064] = 'ER_METADATA_INCONSISTENCY';
codes[4065] = 'ER_CF_DIFFERENT';
codes[4066] = 'ER_RDB_TTL_DURATION_FORMAT';
codes[4067] = 'ER_RDB_STATUS_GENERAL';
codes[4068] = 'ER_RDB_STATUS_MSG';
codes[4069] = 'ER_RDB_TTL_UNSUPPORTED';
codes[4070] = 'ER_RDB_TTL_COL_FORMAT';
codes[4071] = 'ER_PER_INDEX_CF_DEPRECATED';
codes[4072] = 'ER_KEY_CREATE_DURING_ALTER';
codes[4073] = 'ER_SK_POPULATE_DURING_ALTER';
codes[4074] = 'ER_SUM_FUNC_WITH_WINDOW_FUNC_AS_ARG';
codes[4075] = 'ER_NET_OK_PACKET_TOO_LARGE';
codes[4076] = 'ER_GEOJSON_EMPTY_COORDINATES';
codes[4077] = 'ER_MYROCKS_CANT_NOPAD_COLLATION';
codes[4078] = 'ER_ILLEGAL_PARAMETER_DATA_TYPES2_FOR_OPERATION';
codes[4079] = 'ER_ILLEGAL_PARAMETER_DATA_TYPE_FOR_OPERATION';
codes[4080] = 'ER_WRONG_PARAMCOUNT_TO_CURSOR';
codes[4081] = 'ER_UNKNOWN_STRUCTURED_VARIABLE';
codes[4082] = 'ER_ROW_VARIABLE_DOES_NOT_HAVE_FIELD';
codes[4083] = 'ER_END_IDENTIFIER_DOES_NOT_MATCH';
codes[4084] = 'ER_SEQUENCE_RUN_OUT';
codes[4085] = 'ER_SEQUENCE_INVALID_DATA';
codes[4086] = 'ER_SEQUENCE_INVALID_TABLE_STRUCTURE';
codes[4087] = 'ER_SEQUENCE_ACCESS_ERROR';
codes[4088] = 'ER_SEQUENCE_BINLOG_FORMAT';
codes[4089] = 'ER_NOT_SEQUENCE';
codes[4090] = 'ER_NOT_SEQUENCE2';
codes[4091] = 'ER_UNKNOWN_SEQUENCES';
codes[4092] = 'ER_UNKNOWN_VIEW';
codes[4093] = 'ER_WRONG_INSERT_INTO_SEQUENCE';
codes[4094] = 'ER_SP_STACK_TRACE';
codes[4095] = 'ER_PACKAGE_ROUTINE_IN_SPEC_NOT_DEFINED_IN_BODY';
codes[4096] = 'ER_PACKAGE_ROUTINE_FORWARD_DECLARATION_NOT_DEFINED';
codes[4097] = 'ER_COMPRESSED_COLUMN_USED_AS_KEY';
codes[4098] = 'ER_UNKNOWN_COMPRESSION_METHOD';
codes[4099] = 'ER_WRONG_NUMBER_OF_VALUES_IN_TVC';
codes[4100] = 'ER_FIELD_REFERENCE_IN_TVC';
codes[4101] = 'ER_WRONG_TYPE_FOR_PERCENTILE_FUNC';
codes[4102] = 'ER_ARGUMENT_NOT_CONSTANT';
codes[4103] = 'ER_ARGUMENT_OUT_OF_RANGE';
codes[4104] = 'ER_WRONG_TYPE_OF_ARGUMENT';
codes[4105] = 'ER_NOT_AGGREGATE_FUNCTION';
codes[4106] = 'ER_INVALID_AGGREGATE_FUNCTION';
codes[4107] = 'ER_INVALID_VALUE_TO_LIMIT';
codes[4108] = 'ER_INVISIBLE_NOT_NULL_WITHOUT_DEFAULT';
codes[4109] = 'ER_UPDATE_INFO_WITH_SYSTEM_VERSIONING';
codes[4110] = 'ER_VERS_FIELD_WRONG_TYPE';
codes[4111] = 'ER_VERS_ENGINE_UNSUPPORTED';
codes[4112] = 'ER_UNUSED_23';
codes[4113] = 'ER_PARTITION_WRONG_TYPE';
codes[4114] = 'WARN_VERS_PART_FULL';
codes[4115] = 'WARN_VERS_PARAMETERS';
codes[4116] = 'ER_VERS_DROP_PARTITION_INTERVAL';
codes[4117] = 'ER_UNUSED_25';
codes[4118] = 'WARN_VERS_PART_NON_HISTORICAL';
codes[4119] = 'ER_VERS_ALTER_NOT_ALLOWED';
codes[4120] = 'ER_VERS_ALTER_ENGINE_PROHIBITED';
codes[4121] = 'ER_VERS_RANGE_PROHIBITED';
codes[4122] = 'ER_CONFLICTING_FOR_SYSTEM_TIME';
codes[4123] = 'ER_VERS_TABLE_MUST_HAVE_COLUMNS';
codes[4124] = 'ER_VERS_NOT_VERSIONED';
codes[4125] = 'ER_MISSING';
codes[4126] = 'ER_VERS_PERIOD_COLUMNS';
codes[4127] = 'ER_PART_WRONG_VALUE';
codes[4128] = 'ER_VERS_WRONG_PARTS';
codes[4129] = 'ER_VERS_NO_TRX_ID';
codes[4130] = 'ER_VERS_ALTER_SYSTEM_FIELD';
codes[4131] = 'ER_DROP_VERSIONING_SYSTEM_TIME_PARTITION';
codes[4132] = 'ER_VERS_DB_NOT_SUPPORTED';
codes[4133] = 'ER_VERS_TRT_IS_DISABLED';
codes[4134] = 'ER_VERS_DUPLICATE_ROW_START_END';
codes[4135] = 'ER_VERS_ALREADY_VERSIONED';
codes[4136] = 'ER_UNUSED_24';
codes[4137] = 'ER_VERS_NOT_SUPPORTED';
codes[4138] = 'ER_VERS_TRX_PART_HISTORIC_ROW_NOT_SUPPORTED';
codes[4139] = 'ER_INDEX_FILE_FULL';
codes[4140] = 'ER_UPDATED_COLUMN_ONLY_ONCE';
codes[4141] = 'ER_EMPTY_ROW_IN_TVC';
codes[4142] = 'ER_VERS_QUERY_IN_PARTITION';
codes[4143] = 'ER_KEY_DOESNT_SUPPORT';
codes[4144] = 'ER_ALTER_OPERATION_TABLE_OPTIONS_NEED_REBUILD';
codes[4145] = 'ER_BACKUP_LOCK_IS_ACTIVE';
codes[4146] = 'ER_BACKUP_NOT_RUNNING';
codes[4147] = 'ER_BACKUP_WRONG_STAGE';
codes[4148] = 'ER_BACKUP_STAGE_FAILED';
codes[4149] = 'ER_BACKUP_UNKNOWN_STAGE';
codes[4150] = 'ER_USER_IS_BLOCKED';
codes[4151] = 'ER_ACCOUNT_HAS_BEEN_LOCKED';
codes[4152] = 'ER_PERIOD_TEMPORARY_NOT_ALLOWED';
codes[4153] = 'ER_PERIOD_TYPES_MISMATCH';
codes[4154] = 'ER_MORE_THAN_ONE_PERIOD';
codes[4155] = 'ER_PERIOD_FIELD_WRONG_ATTRIBUTES';
codes[4156] = 'ER_PERIOD_NOT_FOUND';
codes[4157] = 'ER_PERIOD_COLUMNS_UPDATED';
codes[4158] = 'ER_PERIOD_CONSTRAINT_DROP';
codes[4159] = 'ER_TOO_LONG_KEYPART';
codes[4160] = 'ER_TOO_LONG_DATABASE_COMMENT';
codes[4161] = 'ER_UNKNOWN_DATA_TYPE';
codes[4162] = 'ER_UNKNOWN_OPERATOR';
codes[4163] = 'ER_WARN_HISTORY_ROW_START_TIME';
codes[4164] = 'ER_PART_STARTS_BEYOND_INTERVAL';
codes[4165] = 'ER_GALERA_REPLICATION_NOT_SUPPORTED';
codes[4166] = 'ER_LOAD_INFILE_CAPABILITY_DISABLED';
codes[4167] = 'ER_NO_SECURE_TRANSPORTS_CONFIGURED';
codes[4168] = 'ER_SLAVE_IGNORED_SHARED_TABLE';
codes[4169] = 'ER_NO_AUTOINCREMENT_WITH_UNIQUE';
codes[4170] = 'ER_KEY_CONTAINS_PERIOD_FIELDS';
codes[4171] = 'ER_KEY_CANT_HAVE_WITHOUT_OVERLAPS';
codes[4172] = 'ER_NOT_ALLOWED_IN_THIS_CONTEXT';
codes[4173] = 'ER_DATA_WAS_COMMITED_UNDER_ROLLBACK';
codes[4174] = 'ER_PK_INDEX_CANT_BE_IGNORED';
codes[4175] = 'ER_BINLOG_UNSAFE_SKIP_LOCKED';
codes[4176] = 'ER_JSON_TABLE_ERROR_ON_FIELD';
codes[4177] = 'ER_JSON_TABLE_ALIAS_REQUIRED';
codes[4178] = 'ER_JSON_TABLE_SCALAR_EXPECTED';
codes[4179] = 'ER_JSON_TABLE_MULTIPLE_MATCHES';
codes[4180] = 'ER_WITH_TIES_NEEDS_ORDER';
codes[4181] = 'ER_REMOVED_ORPHAN_TRIGGER';
codes[4182] = 'ER_STORAGE_ENGINE_DISABLED';
codes[4183] = 'WARN_SFORMAT_ERROR';
codes[4184] = 'ER_PARTITION_CONVERT_SUBPARTITIONED';
codes[4185] = 'ER_PROVIDER_NOT_LOADED';
codes[4186] = 'ER_JSON_HISTOGRAM_PARSE_FAILED';
codes[4187] = 'ER_SF_OUT_INOUT_ARG_NOT_ALLOWED';
codes[4188] = 'ER_INCONSISTENT_SLAVE_TEMP_TABLE';
codes[4189] = 'ER_VERS_HIST_PART_FAILED';

module.exports.codes = codes;
