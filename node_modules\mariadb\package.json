{"name": "ma<PERSON>b", "version": "3.4.4", "description": "fast mariadb or mysql connector.", "main": "promise.js", "types": "types/index.d.ts", "typesVersions": {"*": {"callback": ["types/callback.d.ts"], "*": ["types/index.d.ts"]}}, "directories": {"lib": "lib", "test": "test"}, "private": false, "scripts": {"test": "npm run test:types-prettier && npm run test:prettier && npm run test:types && npm run test:lint && npm run test:base", "test:base": "mocha --no-parallel --timeout 5000 \"test/**/*.js\"", "test:lint": "eslint \"*.js\" \"{lib,test}/**/*.js\"", "test:types": "eslint \"types/*.ts\"", "test:types-prettier": "prettier --write \"types/*.ts\"", "test:prettier": "prettier --write \"*.js\" \"{tools,lib,test,benchmarks}/**/*.js\"", "coverage": "npm run coverage:test && npm run coverage:create && npm run coverage:send", "coverage:test": "nyc mocha --no-parallel --timeout 5000 \"test/**/*.js\"", "coverage:report": "npm run coverage:create && npm run coverage:send", "coverage:create": "nyc report --reporter=text-lcov > coverage.lcov", "coverage:send": "./codecov --disable=gcov", "benchmark": "node benchmarks/benchmarks-all.js", "generate": "node ./tools/generate-mariadb.js"}, "repository": {"type": "git", "url": "git+https://github.com/mariadb-corporation/mariadb-connector-nodejs.git"}, "keywords": ["ma<PERSON>b", "mysql", "client", "driver", "connector"], "files": ["lib", "types/index.d.ts", "types/callback.d.ts", "types/share.d.ts", "promise.js", "check-node.js", "callback.js"], "engines": {"node": ">= 14"}, "author": "<PERSON> <<EMAIL>>", "license": "LGPL-2.1-or-later", "dependencies": {"denque": "^2.1.0", "iconv-lite": "^0.6.3", "lru-cache": "^10.3.0"}, "devDependencies": {"@types/geojson": "^7946.0.14", "@types/node": "^22.5.4", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "benchmark": "^2.1.4", "chai": "^4.4.1", "chalk": "^4.1.2", "error-stack-parser": "^2.1.4", "eslint": "^8.48.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-markdown": "^3.0.1", "eslint-plugin-prettier": "^5.0.0", "mocha": "^10.2.0", "mocha-lcov-reporter": "^1.3.0", "nyc": "^15.1.0", "prettier": "^3.0.3", "typescript": "^5.6.2", "winston": "^3.10.0"}, "bugs": {"url": "https://jira.mariadb.org/projects/CONJS/"}, "homepage": "https://github.com/mariadb-corporation/mariadb-connector-nodejs#readme"}