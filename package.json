{"name": "backend-api", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test-db": "node test-db.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "mariadb": "^3.4.4", "mysql2": "^3.14.1"}}