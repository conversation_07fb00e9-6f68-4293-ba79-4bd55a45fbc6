# Backend API Server

A Node.js Express server with MySQL database integration for student registration and management.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Configure Database
Copy the `.env` file and update database credentials:
```env
DB_HOST=localhost
DB_USER=lms_user
DB_PASS=lms2026
DB_NAME=dblibrary
```

### 3. Set Up Database
Follow the instructions in `DATABASE_SETUP.md` to create the database and tables.

### 4. Test Database Connection
```bash
npm run test-db
```

### 5. Start Server
```bash
npm start
```

The server will start on `http://localhost:3000`

## 📋 API Endpoints

### Health Check
- **GET** `/`
- Returns server status and timestamp

### Student Registration
- **POST** `/api/auth/register-student`
- Registers a new student in the database

### Get Single Student
- **GET** `/api/auth/get-student/:studentID`
- Retrieves a specific student by ID

### Get All Students
- **GET** `/api/auth/get-all-students`
- Retrieves all students from the database

### Update Student
- **PUT** `/api/auth/update-student/:studentID`
- Updates an existing student's information

### Delete Student
- **DELETE** `/api/auth/delete-student/:studentID`
- Deletes a student from the database

#### Request Body:
```json
{
  "fullName": "John Doe",
  "email": "<EMAIL>",
  "password": "securePassword123",
  "course": "BSIT",
  "yearLevel": 3,
  "section": "A",
  "phoneNumber": "09123456789"
}
```

#### Required Fields:
- `fullName` (string)
- `email` (string, must be unique)
- `password` (string, will be hashed)

#### Optional Fields:
- `course` (string, defaults to "N/A")
- `yearLevel` (number, defaults to 0)
- `section` (string, defaults to "N/A")
- `phoneNumber` (string, defaults to "N/A")

#### Response:
```json
{
  "message": "✅ Student registered successfully",
  "studentID": 123
}
```

## 🧪 Testing

### Register Student:
```bash
curl -X POST http://localhost:3000/api/auth/register-student \
  -H "Content-Type: application/json" \
  -d '{
    "studentID": "2025-00001",
    "fullName": "Jane Smith",
    "email": "<EMAIL>",
    "password": "mySecurePass123",
    "course": "BSCS",
    "yearLevel": 2,
    "section": "B",
    "phoneNumber": "09987654321"
  }'
```

### Get Student:
```bash
curl http://localhost:3000/api/auth/get-student/2025-00001
```

### Get All Students:
```bash
curl http://localhost:3000/api/auth/get-all-students
```

### Update Student:
```bash
curl -X PUT http://localhost:3000/api/auth/update-student/2025-00001 \
  -H "Content-Type: application/json" \
  -d '{
    "fullName": "Jane Updated",
    "course": "BSIT",
    "yearLevel": 3
  }'
```

### Delete Student:
```bash
curl -X DELETE http://localhost:3000/api/auth/delete-student/2025-00001
```

### Test with PowerShell:
```powershell
Invoke-RestMethod -Uri "http://localhost:3000/api/auth/register-student" `
  -Method POST `
  -ContentType "application/json" `
  -Body '{"fullName":"Test User","email":"<EMAIL>","password":"test123"}'
```

## 🛠️ Available Scripts

- `npm start` - Start the production server
- `npm run dev` - Start the development server
- `npm run test-db` - Test database connection and setup

## 🔧 Troubleshooting

### Database Connection Issues
1. Run `npm run test-db` to diagnose connection problems
2. Check if MySQL/MariaDB is running
3. Verify credentials in `.env` file
4. Follow `DATABASE_SETUP.md` for database setup

### Common Errors
- **Access Denied**: Check database user credentials
- **Database Not Found**: Create the database using setup guide
- **Connection Refused**: Ensure MySQL service is running

## 📁 Project Structure

```
backend-api/
├── server.js          # Main server file
├── db.js             # Database connection
├── routes/
│   └── auth.js       # Authentication routes
├── .env              # Environment variables
├── package.json      # Dependencies and scripts
├── test-db.js        # Database connection test
├── DATABASE_SETUP.md # Database setup guide
└── README.md         # This file
```

## 🔒 Security Features

- Password hashing with bcryptjs
- Input validation and sanitization
- Error handling and logging
- CORS enabled for cross-origin requests

## 📝 Notes

- The server will start even if database connection fails
- Database errors are logged but don't crash the server
- All passwords are automatically hashed before storage
- Student IDs are auto-generated by the database
